<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * OtpVerifications Model
 *
 * @method \App\Model\Entity\OtpVerification newEmptyEntity()
 * @method \App\Model\Entity\OtpVerification newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\OtpVerification> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\OtpVerification get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\OtpVerification findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\OtpVerification patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\OtpVerification> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\OtpVerification|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\OtpVerification saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\OtpVerification>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OtpVerification>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OtpVerification>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OtpVerification> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OtpVerification>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OtpVerification>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\OtpVerification>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\OtpVerification> deleteManyOrFail(iterable $entities, array $options = [])
 */
class OtpVerificationsTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('otp_verifications');
        $this->setDisplayField('user_identifier');
        $this->setPrimaryKey('id');
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->scalar('user_identifier')
            ->maxLength('user_identifier', 255)
            ->requirePresence('user_identifier', 'create')
            ->notEmptyString('user_identifier');

        $validator
            ->scalar('otp')
            ->maxLength('otp', 6)
            ->requirePresence('otp', 'create')
            ->notEmptyString('otp');

        $validator
            ->scalar('otp_type')
            ->requirePresence('otp_type', 'create')
            ->notEmptyString('otp_type');

        $validator
            ->dateTime('created_at')
            ->requirePresence('created_at', 'create')
            ->notEmptyDateTime('created_at');

        $validator
            ->dateTime('expires_at')
            ->requirePresence('expires_at', 'create')
            ->notEmptyDateTime('expires_at');

        $validator
            ->boolean('is_used')
            ->allowEmptyString('is_used');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->isUnique(['user_identifier', 'otp']), ['errorField' => 'user_identifier']);

        return $rules;
    }

    //S
    public function save_otp($user_identifier, $otp, $otp_type)
    {
        $existing_otp = $this->find()->where(['user_identifier' => $user_identifier, 'otp_type' => $otp_type, 'is_used' => 0])->first();
        if (!$existing_otp) {
            $saveOtp = $this->newEmptyEntity();
        } else {
            $saveOtp = $this->get($existing_otp['id']);
        }
        $saveOtp['user_identifier'] = $user_identifier;
        $saveOtp['otp'] = $otp;
        $saveOtp['otp_type'] = $otp_type;
        $saveOtp['created_at'] = date('Y-m-d H:i:s');
        $saveOtp['expires_at'] = date('Y-m-d H:i:s', strtotime('+5 minutes')); // OTP valid for 10 minutes
        if ($this->save($saveOtp)) {
            return true;
        } else {
            return false;
        }
    }

    //S
    public function verify_otp($user_identifier, $otp)
    {
        $detail = $this->find('all', [
            'conditions' => [
                'user_identifier' => $user_identifier,
                'otp' => $otp,
                'is_used' => 0
            ],
            'order' => ['created_at' => 'DESC'] 
        ])->disableHydration()->first();

        if ($detail) {

            $expires_at = date('Y-m-d H:i:s', strtotime((string)$detail['expires_at']));

            if (date('Y-m-d H:i:s') < $expires_at) {
                // Mark the OTP as used
                $update['is_used'] = 1;
                $update_record = $this->get($detail['id']);
                $update_record = $this->patchEntity($update_record, $update);
                $this->save($update_record);
                $result = 1;
            } else {
                $result = 2;
            }
        } else {
            $result = 3;
        }
        return $result;
    }

        //ax
        public function save_otp_web($user_identifier, $otp, $otp_type)
        {
            $existing_otp = $this->find()->where(['user_identifier' => $user_identifier, 'otp_type' => $otp_type, 'is_used' => 0])->first();
            if (!$existing_otp) {
                $saveOtp = $this->newEmptyEntity();
            } else {
                $saveOtp = $this->get($existing_otp['id']);
            }
            $saveOtp['user_identifier'] = $user_identifier;
            $saveOtp['otp'] = $otp;
            $saveOtp['otp_type'] = $otp_type;
            $saveOtp['created_at'] = date('Y-m-d H:i:s');
            $saveOtp['expires_at'] = date('Y-m-d H:i:s', strtotime('+1 minute')); // OTP valid for 1 minute
            if ($this->save($saveOtp)) {
                return true;
            } else {
                return false;
            }
        }
    
}
