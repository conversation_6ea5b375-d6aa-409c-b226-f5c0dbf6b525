<?php $this->start('add_css'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/newAccout.css') ?>">
<link rel="stylesheet" href="<?= $this->Url->webroot('css/otpVerify.css') ?>">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<style>
    .verif-code-input{
        padding-left:10px;
    } 
    .verif-code-input::placeholder {
        padding-left: 0px !important;
    }
    .sign-up-links-input-mail{
        padding-left:10px;
    } 
    .sign-up-links-input-mail::placeholder {
        padding-left: 0px !important;
    }
    label.error {
        display: block;
        text-align: left;
        color: red;
        /* padding-left: 48px; */
        padding-left: 28px;
    }
    p.enter-verification-code.label-text-2 {
        margin-left: 28px;
    }
    p.enter-verification-code.label-text-3 {
        margin-left: 28px;
    }
    .sign-up-links-input-mail {
        margin-top: 0px !important;
    }
    #account-body-subcontainer {
        padding-bottom: 20px;
    }
    .password-container{
        position: relative;
    }
    .toggle-password{
        position: absolute;
        right: 42px;
        top: 10px;
        margin-top: 0;
        font-weight: 400 !important;
        font-size: 17px;
    }
</style>

<?php $this->end(); ?>
<accountbody class="account-body">

    <div class="account-body-subcontainer" id="account-body-subcontainer">
        <div class="camera-img-container">
            <img src="<?= $this->Url->webroot('assets/camera.png') ?>" class="camera-img">
        </div>
        <?= $this->Form->create(null, ['id' => 'otpForm', 'novalidate' => true]) ?>
        <?= $this->Flash->render() ?>
        <div class=" account-body-texts">
            <p class="looks-like-you-new"><?= __("Looks like you're new here!") ?></p>
            <p class="sign-up-with-your-mobile"><?= __("Sign up with your mail ID or mobile number to get started") ?></p>
            <div class="please-enter-verif-code-container">
                <p class="please-enter-verif-code"><?= __("Please enter the Verification Code to Verify your account.") ?></p>
                <p class="code-sent"><?= __("A Code has been sent to the registered $type") ?></p>
            </div>
            <div class="verification-code-container">
                <p class="enter-verification-code label-text-1"><?= __('Enter Verification Code') ?></p>

                <input class="verif-code-input" name='otp' maxlength="4" id='otp' placeholder="<?= __('Enter Verification Code') ?>" required>

                <input type="hidden" name='email' value="<?php echo $email; ?>" style="display: block;">
                <input type="hidden" name='mobile' value="<?php echo $mobileNum; ?>" style="display: block;">
                <input type="hidden" name='country_code' value="<?php echo $countryCode; ?>">
                <input type="hidden" name='type' value="<?php echo $type; ?>">

                <p class="enter-verification-code label-text-2"><?= __('Enter Password') ?></p>
                <div class="password-container">
                    <i class="fas fa-eye-slash toggle-password" id="togglePassword"></i>
                    <input type="password" name="password" id="password" class="sign-up-links-input-mail" placeholder="<?= __('Enter Password') ?>">
                </div>
                <p class="enter-verification-code label-text-3"><?= __('Confirm Password') ?></p>
                <div class="password-container">
                    <i class="fas fa-eye-slash toggle-password" id="toggleConfirmPassword"></i>
                    <input type="password" name="confirm_password" id="confirm_password" class="sign-up-links-input-mail" placeholder="<?= __('Confirm Password') ?>"><br />
                </div>

                <button type="submit" class="verify-button"><?= __('Verify') ?></button>

                <?php
                $currentTime = new DateTime();
                $expiryTime = $expiryotptimer->format('Y-m-d H:i:s');
                ?>

                <div class="not-recived-error"><?= __("Not Received Your Code?") ?> : <span id="countdown"></span></div>



            </div>
        </div>
        <?= $this->Form->end() ?>
    </div>
</accountbody>
<?php $this->start('add_js'); ?>
<script>
    $("#togglePassword").click(function(){
        if($("input#password").attr("type") === "password"){
            $("input#password").attr("type","text");
            $(this).addClass("fa-eye").removeClass("fa-eye-slash");
        }
        else{
            $("input#password").attr("type","password");
            $(this).addClass("fa-eye-slash").removeClass("fa-eye");
        }
    });


    $("#toggleConfirmPassword").click(function(){
    if($("input#confirm_password").attr("type") === "password"){
            $("input#confirm_password").attr("type","text");
            $(this).addClass("fa-eye").removeClass("fa-eye-slash");
        }
        else{
            $("input#confirm_password").attr("type","password");
            $(this).addClass("fa-eye-slash").removeClass("fa-eye");
        }
    });
</script>
<?php $this->end(); ?>
<script>
$(document).ready(function () {
    const delayMillis = 1 * 60 * 1000; // 1 minute in milliseconds
    const storageKey = "otpCountdownTargetTime";

    // If no target time is stored, set it now
    if (!localStorage.getItem(storageKey)) {
        const newTargetTime = new Date().getTime() + delayMillis;
        localStorage.setItem(storageKey, newTargetTime);
    }

    function updateTimer() {
        const now = new Date().getTime();
        const targetTime = parseInt(localStorage.getItem(storageKey) || 0);
        const difference = targetTime - now;

        if (difference <= 0) {
            $("#countdown").html(
                '<a href="javascript:location.reload();"><span style="color:#0D839B;"> <?= __("Resend Code") ?> </span></a>'
            );
            localStorage.removeItem(storageKey); // Optional: clear after complete
            clearInterval(timer);
            return;
        }

        const minutes = Math.floor(difference / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        $("#countdown").html(
            `<span style='color: darkgrey'>Resend Code ${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}</span>`
        );
    }

    updateTimer(); // Initial call
    const timer = setInterval(updateTimer, 1000); // Every second
});
</script>


<script>
    $(document).ready(function() {

        // Set up validation rules
        $("#otpForm").validate({
            rules: {
                otp: {
                    required: true,
                    digits: true,
                    minlength: 4,
                    maxlength: 6
                }, password: {
                    required: true,
                    minlength: 6
                },
                confirm_password: {
                    required: true,
                    equalTo: "#password"
                }
            },
            messages: {
                otp: {
                    required: "OTP is required.",
                    digits: "OTP must be numeric.",
                    minlength: "OTP must be at least 4 digits.",
                    maxlength: "OTP must not exceed 4 digits."
                },
                password: {
                    required: "Please provide a password.",
                    minlength: "Your password must be at least 6 characters long."
                },
                confirm_password: {
                    required: "Please confirm your password.",
                    equalTo: "Passwords do not match."
                }
            },
            submitHandler: function(form) {
                form.submit();
            }
        });
    });

</script>
