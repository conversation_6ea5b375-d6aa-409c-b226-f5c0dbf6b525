<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;

/**
 * CashDeskClosures Model
 *
 * @property \App\Model\Table\CashDesksTable&\Cake\ORM\Association\BelongsTo $CashDesks
 * @property \App\Model\Table\ShowroomsTable&\Cake\ORM\Association\BelongsTo $Showrooms
 *
 * @method \App\Model\Entity\CashDeskClosure newEmptyEntity()
 * @method \App\Model\Entity\CashDeskClosure newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\CashDeskClosure> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\CashDeskClosure get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\CashDeskClosure findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\CashDeskClosure patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\CashDeskClosure> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\CashDeskClosure|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\CashDeskClosure saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\CashDeskClosure>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashDeskClosure>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashDeskClosure>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashDeskClosure> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashDeskClosure>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashDeskClosure>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\CashDeskClosure>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\CashDeskClosure> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CashDeskClosuresTable extends Table
{
    /**
     * Initialize method
     *
     * @param array<string, mixed> $config The configuration for the Table.
     * @return void
     */
    public function initialize(array $config): void
    {
        parent::initialize($config);

        $this->setTable('cash_desk_closures');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('CashDesks', [
            'foreignKey' => 'cash_desk_id',
        ]);
        $this->belongsTo('Showrooms', [
            'foreignKey' => 'showroom_id',
        ]);
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('cash_desk_id')
            ->allowEmptyString('cash_desk_id');

        $validator
            ->integer('showroom_id')
            ->allowEmptyString('showroom_id');

        $validator
            ->dateTime('closing_time')
            ->requirePresence('closing_time', 'create')
            ->notEmptyDateTime('closing_time');

        $validator
            ->integer('closed_by')
            ->requirePresence('closed_by', 'create')
            ->notEmptyString('closed_by');

        $validator
            ->decimal('opening_balance')
            ->requirePresence('opening_balance', 'create')
            ->notEmptyString('opening_balance');

        $validator
            ->decimal('closing_balance')
            ->requirePresence('closing_balance', 'create')
            ->notEmptyString('closing_balance');

        $validator
            ->decimal('total_cash_in')
            ->requirePresence('total_cash_in', 'create')
            ->notEmptyString('total_cash_in');

        $validator
            ->decimal('total_cash_out')
            ->requirePresence('total_cash_out', 'create')
            ->notEmptyString('total_cash_out');

        $validator
            ->decimal('total_sales')
            ->requirePresence('total_sales', 'create')
            ->notEmptyString('total_sales');

        $validator
            ->decimal('total_cash')
            ->requirePresence('total_cash', 'create')
            ->notEmptyString('total_cash');

        $validator
            ->scalar('notes')
            ->allowEmptyString('notes');

        return $validator;
    }

    /**
     * Returns a rules checker object that will be used for validating
     * application integrity.
     *
     * @param \Cake\ORM\RulesChecker $rules The rules object to be modified.
     * @return \Cake\ORM\RulesChecker
     */
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['cash_desk_id'], 'CashDesks'), ['errorField' => 'cash_desk_id']);
        $rules->add($rules->existsIn(['showroom_id'], 'Showrooms'), ['errorField' => 'showroom_id']);

        return $rules;
    }

    public function cashDeskClose($data) {

        $cash_desk = $this->newEmptyEntity();
        $cash_desk['showroom_id'] = $data['showroom_id'];
        $cash_desk['closing_time'] = date('Y-m-d H:i:s');
        $cash_desk['closed_by'] = $data['closed_by'];
        $cash_desk['opening_balance'] = $data['opening_balance'];
        $cash_desk['closing_balance'] = $data['closing_balance'];
        //$cash_desk['total_cash_in'] = '';
        //$cash_desk['total_cash_out'] = '';
        //$cash_desk['total_sales'] = '';
        $cash_desk['total_cash'] = $data['total_cash'];
        $cash_desk['status'] = $data['status'];
        /*if($data['notes']) {
          $cash_desk['notes'] = $data['notes'];
        }*/        
        $save_cash_desk = $this->save($cash_desk); 
        if($save_cash_desk) {
            return true;
        } else {
            return false;
        }
    }
}
