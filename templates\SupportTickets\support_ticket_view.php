<?php
// print_r($supportTicket['support_category_id']); die;
/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\SupportTicket $supportTicket
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.css') ?>">
<link href="https://cdn.jsdelivr.net/npm/lightgallery@2.5.0/css/lightgallery-bundle.min.css" rel="stylesheet">
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/select2/dist/css/select2.min.css') ?>">
<style>
    .is-invalid-ckeditor {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }

    .is-invalid-select {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + .75rem);
        background-image: url('data:image/svg+xml,%3csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none" stroke="%23dc3545"%3e%3ccircle cx="6" cy="6" r="4.5" /%3e%3cpath stroke-linejoin="round" d="M5.8 3.6h.4L6 6.5z" /%3e%3ccircle cx="6" cy="8.2" r=".6" fill="%23dc3545" stroke="none" /%3e%3c/svg%3e');
        background-repeat: no-repeat;
        background-position: right calc(.375em + .1875rem) center;
        background-size: calc(.75em + .375rem) calc(.75em + .375rem);
    }
</style>
<?php $this->end(); ?>
 <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard') ?></h4>
            </li>
            <li class="breadcrumb-item active">
                <?= __("Support Ticket Details") ?>
            </li>
        </ul>
        <button onclick="window.location.href='/support-tickets/list?category=<?= $supportTicket['support_category_id'] ?>'" class="d-flex align-items-center" id="back-button-mo">
                <small class="p-10 fw-bold"><?= __('BACK') ?></small>
                <span class="rotate me-2">⤣</span>
            </button>
</div>



<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title"><?= __('Support Ticket Details') ?> - <?= h($supportTicket->ticketID) ?></h4>
                
            </div>
            <div class="card-body">
                <div class="ticket-info">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong><?= __('Issue:') ?></strong> <?= h($supportTicket->issue_name) ?>
                        </div>
                        <div class="col-md-4">
                            <strong><?= __('Priority:') ?></strong> <?= h($supportTicket->priority) ?>
                        </div>
                        <div class="col-md-4">
                            <strong><?= __('Created:') ?></strong> <?= $supportTicket->created ? $supportTicket->created->format($dateFormat) .' '. $supportTicket->created->format($timeFormat) : __("N/A") ?>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong><?= __('Category:') ?></strong> <?= $supportTicket->support_category->name ?? __("N/A") ?>
                        </div>
                        <div class="col-md-4">
                            <strong><?= __('Created By:') ?></strong> <?= $supportTicket->created_by_user->first_name ?? __("N/A") ?>
                        </div>
                        <div class="col-md-4">
                            <strong><?= __('Status:') ?></strong>     
                            <div class="status-badge <?= strtolower(str_replace(' ', '-', $supportTicket->status)) ?>">
                                <?= h($supportTicket->status) ?>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-3">
                            <strong><?= __("Update Status") ?>:</strong>
                            <div class="mt-2">
                                <?= $this->Form->create($supportTicket, ['url' => ['controller' => 'SupportTickets', 'action' => 'updateStatus']]) ?>
                                <?= $this->Form->hidden('id', ['value' => $supportTicket->id]) ?>
                                <div class="form-group mb-3">
                                    <?= $this->Form->control('status', [
                                        'type' => 'select',
                                        'options' => ['Open' => 'Open', 'In Progress' => 'In Progress', 'Resolved' => 'Resolved', 'Closed' => 'Closed'],
                                        'class' => 'form-control form-select select2',
                                        'label' => __('Choose Status')
                                    ]) ?>
                                </div>
                                <div class="form-group mb-3">
                                    <?= $this->Form->control('customer_id', [
                                        'type' => 'select',
                                        'options' => $customers->map(function ($customer) {
                                            return [
                                                'value' => $customer->customer->id,
                                                'text' => $customer->first_name . ' ' . $customer->last_name . 
                                                          ($customer->email ? ' - ' . $customer->email : '') . 
                                                          ($customer->mobile_no ? ' - ' . $customer->mobile_no : '')
                                            ];
                                        })->toArray(),
                                        'empty' => __('Select Issuer Customer'),
                                        'class' => 'form-control form-select select2',
                                        'label' => __('Select Issuer Customer'),
                                        'data-live-search' => 'true'
                                    ]) ?>
                                </div>
                                <?= $this->Form->button('Update Status', ['class' => 'btn btn-primary mt-3']) ?>
                                <?= $this->Form->end() ?>
                            </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <strong><?= __('Description:') ?></strong>
                            <div class="description-box mt-2">
                                <?= nl2br(h($supportTicket->description ?? __('N/A'))) ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attachments Section -->
                <?php if (!empty($supportTicket->support_ticket_images)): ?>
                <div class="attachments-section mb-4">
                    <h5><?= __('Attachments') ?></h5>
                    <div class="row">
                        <?php foreach ($supportTicket->support_ticket_images as $image): ?>
                        <div class="col-md-1 mb-3">
                            <div class="attachment-box-2">
                                <?php if (!empty($image->image)) : ?>
                                    <?php if (in_array(strtolower(pathinfo($image->image, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                        <a href="<?= $this->WishListView->getCloudFrontURLView($image->image); ?>" target="_blank" >
                                            <img src="<?= $this->WishListView->getCloudFrontURLView($image->image); ?>" class="img-thumbnail" alt="Attachment" >
                                        </a>
                                    <?php else: ?>
                                        <a href="<?= $this->WishListView->getCloudFrontURLView($image->image); ?>" target="_blank" class="btn btn-secondary">
                                            <?= __('Download Attachment') ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Conversation History -->
                <div class="conversation-section">
                    <h5><?= __('Conversation History') ?></h5>
                    <div class="conversation-thread mb-4">
                        <?php if (!empty($supportTicket->support_ticket_updates)): ?>
                            <?php foreach ($supportTicket->support_ticket_updates as $update): ?>
                            <div class="message-box <?= h($update->updated_by_user->_matchingData['Roles']->name) == 'User' ? 'message-right' : 'message-left' ?>">
                                <div class="message-content">
                                    <div class="message-header">
                                        <strong><?= h($update->updated_by_user->first_name ?? 'Unknown') ?></strong>
                                        <span class="message-time"><?= $update->updated_at ? $update->updated_at->format($dateFormat).' '.$update->updated_at->format($timeFormat) : __("N/A") ?></span>
                                    </div>
                                    <div class="message-text">
                                        <?= nl2br(h($update->comment)) ?>
                                    </div>

                                 
                                    
                                    <?php if (!empty($update['support_ticket_images'])) : ?>
                                        <?php foreach ($update['support_ticket_images'] as $image) : ?>
                                            <?php if (in_array(strtolower(pathinfo($image['image'], PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                <a href="<?= $this->WishListView->getCloudFrontURLView($image['image']); ?>" target="_blank">
                                                    <img src="<?= $this->WishListView->getCloudFrontURLView($image['image']); ?>" width="120" alt="Ticket Image">
                                                </a>
                                            <?php else: ?>
                                                <a href="<?= $this->WishListView->getCloudFrontURLView($image['image']); ?>" target="_blank" class="btn btn-secondary">
                                                    <?= __('Download Attachment') ?>
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    <?php endif; ?>



                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted"><?= __('No updates yet.') ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Add Comment Form -->
                    <?php if ($supportTicket->status !== 'Closed'): ?>
                    <div class="add-comment-section">
                    <?= $this->Form->create(null, [
                    'type' => 'file',
                    'url' => [
                        'controller' => 'SupportTickets',
                        'action' => 'reply'
                    ],
                    'id' => 'reply-form'
                ]) ?>
                          <?= $this->Form->hidden('id', ['value' => $supportTicket['id']]) ?>
                        <div class="form-group">
                            <?= $this->Form->control('description', [
                                'type' => 'textarea',
                                'class' => 'form-control',
                                'placeholder' => __("Type your message here..."),
                                'label' => false,
                                'id' => 'description-field'
                            ]) ?>
                            <div class="invalid-feedback" id="description-error"></div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-5 col-sm-5">
                                <div class="input file">
                                    <input type="file" name="images[]" multiple class="form-control" id="images-field" placeholder="<?= __('Brand Logo') ?>" accept="image/jpg,image/jpeg,image/png,image/svg" aria-label="Brand Logo">
                                </div>
                                <div class="invalid-feedback" id="images-error"></div>
                            </div>
                        </div>
                        <div class="form-validation-message text-danger mt-2" id="form-validation-error" style="display: none;">
                            <?= __('Please provide either a message or attach at least one image.') ?>
                        </div>

                        <div class="form-group mt-3">
                            <?= $this->Form->button('Send Message', ['class' => 'btn btn-primary', 'type' => 'submit']) ?>
                        </div>
                        <?= $this->Form->end() ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->append('script'); ?>
<script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
<script src="<?= $this->Url->webroot('bundles/ckeditor/ckeditor.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/bootstrap-tagsinput/dist/bootstrap-tagsinput.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/lightgallery@2.5.0/lightgallery.umd.js"></script>
<script src="<?= $this->Url->webroot('bundles/select2/dist/js/select2.full.min.js') ?>"></script>
<script src="https://unpkg.com/@zxing/library@latest"></script>
<script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.0/dist/JsBarcode.all.min.js"></script>

<script>
    $(document).ready(function() {
        $('.select2').select2({
            minimumResultsForSearch: 0
        });
    });
</script>

<script>
  $(document).ready(function () {
    $('.conversation-thread.mb-4').scrollTop($('.conversation-thread.mb-4')[0].scrollHeight);

    // Form validation for reply form
    $('#reply-form').on('submit', function(e) {
        var description = $('#description-field').val().trim();
        var images = $('#images-field')[0].files;
        var isValid = true;

        // Reset previous validation states
        $('#description-field').removeClass('is-invalid');
        $('#images-field').removeClass('is-invalid');
        $('#form-validation-error').hide();
        $('#description-error').text('');
        $('#images-error').text('');

        // Check if both fields are empty
        if (description === '' && images.length === 0) {
            isValid = false;
            $('#form-validation-error').show();
            $('#description-field').addClass('is-invalid');
            $('#images-field').addClass('is-invalid');
        }

        if (!isValid) {
            e.preventDefault();
            return false;
        }

        return true;
    });

    // Real-time validation feedback
    $('#description-field, #images-field').on('input change', function() {
        var description = $('#description-field').val().trim();
        var images = $('#images-field')[0].files;

        if (description !== '' || images.length > 0) {
            $('#description-field').removeClass('is-invalid');
            $('#images-field').removeClass('is-invalid');
            $('#form-validation-error').hide();
        }
    });
  });
</script>

<?php $this->end(); ?>
<style>
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    margin-left: 10px;
}
.status-badge.open { background-color: #28a745; }
.status-badge.pending { background-color: #ffc107; }
.status-badge.closed { background-color: #dc3545; }
.status-badge.resolved { background-color:rgb(7, 131, 28); }
.status-badge.in-progress { background-color:rgb(233, 118, 11); }

.description-box {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.attachment-box {
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 4px;
    text-align: center;
}
.attachment-box img {
    max-width: 100%;
    height: auto;
}

.conversation-thread {
    max-height: 500px;
    overflow-y: auto;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.message-box {
    margin-bottom: 15px;
    display: flex;
}

.message-left {
    justify-content: flex-start;
}

.message-right {
    justify-content: flex-end;
}

.message-content {
    max-width: 70%;
    padding: 10px 15px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.message-right .message-content {
    background-color: #007bff;
    color: white;
}

.message-header {
    font-size: 0.9em;
    margin-bottom: 5px;
}

.message-time {
    font-size: 0.8em;
    color: #6c757d;
}

.message-right .message-time {
    color: rgba(255,255,255,0.8);
}

.add-comment-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}
</style>