<style>
.dp-img {
    width: auto !important;
    height: 30.62px !important;
}
.custom-card-star {
    color: #06c270;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
    font-size: 14px;
    font-weight: 500;
    line-height: 16.41px;
    /* margin-left: 10px; */
    margin-left: 12px;
    margin-top: 13px;
    margin-bottom: 14px;
}
.rating-stars .star {
    color: rgb(55, 205, 55);
    font-size: 20px;
    margin-right: 2px;
}
.rating-stars .star:before {
    content: '\2605';
}
.rating-stars .star.empty {
    color: #ddd;
}
.rating-stars .star:before {
    content: '\2605';
}
.rating-stars .star.half:after {
    content: '\2605';
    color: rgb(55, 205, 55);
    position: absolute;
    margin-left: -17px;
    width: 9px;
    overflow: hidden;
}

    .search-results {
    }

    .ax-slider-container {
        width: 830px;
        height: 460px;
        border-radius: 30px;
        overflow: hidden;
        position: relative;
        margin-left: 5px;
        margin-right: 10px;
        z-index: 1;
    }

    .ax-slider {
        width: 100%;
        height: 100%;
        position: relative;
    }

    .ax-slides {
        width: 100%;
        height: 100%;
        display: flex;
        transition: transform 0.5s ease-in-out;
    }

    .ax-slide {
        min-width: 100%;
        height: 100%;
        flex-shrink: 0;
    }

    .ax-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .ax-prev,
    .ax-next {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        padding: 12px;
        cursor: pointer;
        z-index: 2;
        transition: background-color 0.3s;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .ax-prev:hover,
    .ax-next:hover {
        background: rgba(0, 0, 0, 0.7);
    }

    .ax-prev { left: 10px; }
    .ax-next { right: 10px; }

    /* Ensure search results appear above slider */
    .search-container {
        position: relative;
        z-index: 1000;
    }
    .carousal-overall-container .deal-text{
        text-align:left;
        margin-left: -40px;
        display: flex;
        justify-content: center;
    }
    .carousal-overall-container .carousel{
        margin-left: -14px;
        height: 335px;
    }
    .custom-carousel-container .promo-text{
        text-align: left;
        /* margin-left: 135px; */
        margin-left: -40px;
        display: flex;
        justify-content: center;
    }
    .custom-carousel-container .custom-parent-carousel .custom-carousel{
        margin-left: 0px;
        height: 500px;
    }
    .my-carousel-container .New-Arrivals-text{
        margin-top: -25px;
        text-align: left;
        /* margin-left: 135px; */
        margin-left: -40px;
        display: flex;
        justify-content: center;
    }
    .my-carousel-container .my-parent-carousel .my-carousel{
        margin-left: 0px;
    }
    .like-home-box img.sng-crsl-img{
        width: 100%;
    }
    .snd-crsl-img{
        padding-top: 0px;
    }
    .landscape-slider {
        width: 1183px;
        margin-left: 8px;
    }
    .landscper-slider-button-container{
        position: relative;
    }
    button.prev {
        left: 7%;
    }
    button.next {
        margin-right: 6px;
        right: 6%;
    }
    .Special-Offers{
        text-align: left;
        /* margin-left: -40px; */
        margin-left: 16px;
        display: flex;
        justify-content: center;
    }
    .Our-Brands{
        margin-top: -25px;
        text-align: left;
        margin-left: -40px;
        display: flex;
        justify-content: center;
    }
    .brnds-container-iner{
        margin: -34px 0px 0px 5px;
    }
    .What-Our-Cli{
        text-align: left;
        margin-left: -40px;
        display: flex;
        justify-content: center;
    }
    .my-carousel-s{
        margin-left: 28px;
    }
    /* .ques-other-parent{
        justify-content: flex-start;
        margin-left: 138px;
    } */
    /* .footer-header-containers{
        justify-content: flex-start;
        margin-left: 58px;
    } */
    /* .footer-flex{
        justify-content: flex-start;
        margin-left: 135px;
    } */
    .custom-carousel-container .clone-custom-parent-carousel .custom-card .custom-product-name{
        min-height: 53px;
    }
    .Deal-of-the-day{
        /* margin-left: -70px; */
        margin-left:-110px;
        display: flex;
        justify-content: center;
    }
    .deal-day{
        position: relative;
        width: 1183px;
        margin: 0 auto;
    }
    .browse-all-product {
        position: absolute;
        top: 40px;
        left: auto;
        right: 0;
    }
    .my-carousel {
        height: 490px;
    }
    .my-carousel-inner-div{
        height: 450px;
    }
    .custom-card-name{
        height: 45px;
    }
    .custom-parent-carousel-cl .custom-carousel .custom-carousel-inner .custom-card{
        height: 434px;
    }
    .custom-carousel-inner{
        height: 480px;
    }
    .deal-of-day-carts .custom-card-subname {
        margin: 5px 0px 5px 20px;
    }
    .p-v-p-item-description-image-description-price-offer {
        margin-left: 8px;
    }
    .deal-of-day-carts .p-v-p-item-description-image-description-price-offer {
        margin-left: 20px;
    }
    .deal-of-day-carts-2 .p-v-p-item-description-image-description-price-offer {
        margin-left: 20px;
    }
    .custom-card-1 {
        padding: 15px 0px;
    }
    .custom-card-subname {
        height: 28px;
    }
    .ques-other-parent {
        gap: 150px;
    }
    .custom-box-2 {
        position: absolute;
        right: 0;
        z-index: 9;
    }
    .p-v-p-item-description-add-to-wishlist-heart {
        /* margin: 8px 19px 0px 0px; */
        padding: 5px;
        margin: 0;
        position: relative;
        right: 19px;
        top: 8px;
        background-color: rgba(255, 255, 255, .6);
        height: 18px;
        border-radius: 50%;
    }
    .like-box-home .p-v-p-item-description-add-to-wishlist-heart {
        /* margin: 8px 19px 0px 0px; */
        padding: 5px;
        margin: 0;
        position: relative;
        right: 0px;
        top: 0px;
        background-color: rgba(255, 255, 255, .6);
        height: 18px;
        border-radius: 50%;
    }
    #snd-crsl-img > a{
        overflow: hidden;
        display: block;
    }
    #snd-crsl-img a img.snd-crsl-img{
        transform: scale(1.39);
    }
    .custom-box {
        position: absolute;
        top: 20px;
        left: 17px;
    }
    .category-links > span{
        position: relative;
        left: 4px;
    }
    .slider img {
        margin-right: 0px;
    }
    .Special-Offers#features-cat{
        margin-left: -50px;
    }
    .prnt-pt-ad .img-wrapper{
        overflow: hidden;
        border-radius: 10px;
    }
    .prnt-pt-ad .img-wrapper .pt-ad {
        transform: scale(1.57);
    }
    span.snd-crsl-img .img-wrapper{
        overflow: hidden;
        border-radius: 15px;
        height: 100%;
    }
    span.snd-crsl-img .img-wrapper img{
        transform: scale(1.23);
    }
    .custom-carousel-inner-class {
        height: 449px;
    }
    .custom-carousel-inner-class .custom-card .img-container{
        overflow: hidden;
    }
    .custom-carousel-inner-class .custom-card .img-container img{
        transform: scale(1.23);
    }
    .parent-carousel .carousel-inner .card .img-wrapper{
        overflow: hidden;
    }
    .parent-carousel .carousel-inner .card .img-wrapper img{
        transform: scale(1.87);
    }
    .my-parent-carousel-s .my-carousel-inner-s .my-card-s{
        height: auto;
    }
</style>

<home class="home">
    <div class="category-container">
        <?php foreach ($categories as $cate): ?>
            <a href="<?= '/product-list/' . $cate['url_key'] ?>" class="category-links"><?= h($cate['name']) ?><span
                    style="color: orange; float: right; font-size: 13px;"
                >&#12297;</span></a>
        <?php endforeach; ?>

    </div>

    <div class="slider-container">
        <!-- <img class="slider" src="./assets/ad-big-scrn.png" /> -->
        <div class="slider-container">
            <div class="slider">
                <?php foreach ($banners as $banner): ?>
                    <?php if(isset($banner['url_link']) && !empty($banner['url_link'])): ?>
                   <a target="_blank" href="<?= h($banner['url_link']) ?>">
                    <img src="<?= h($banner['web_banner']) ?>" alt="<?= h($banner['title'] ?? 'Banner Image') ?>"></a>
                <?php else: ?>
                        <img src="<?= h($banner['web_banner']) ?>" alt="<?= h($banner['title'] ?? 'Banner Image') ?>">
                <?php endif; ?>

                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <div class="aside">
        <div class="aside-top">
            <div class="aside-image-container">
                <a href="tel: +25 21 007 007"><img src="<?= $this->Url->webroot('assets/cart.png') ?>" class="aside-image-icons"/></a>
                <a href="/become-seller"><img src="<?= $this->Url->webroot('assets/shop.png') ?>" class="aside-image-icons"/></a>
                <a href="/shops"><img src="<?= $this->Url->webroot('assets/deal.png') ?>" class="aside-image-icons"/></a>
            </div>
            <div class="aside-text-container">
                <div class="call-number">
                    <a href="tel: +25 21 007 007">
                        <div class="call">Call To Order</div>
                        <div class="number"> 25 21 007 007</div>
                    </a>
                </div>
                <div class="sell">
                    <a href="/become-seller" style="text-decoration: none; color: inherit;">
                        Sell On <span style="color: #EE902C;">BABIKEN</span>
                    </a>
                </div>
                <div class="deals"><a href="/shops"> Our Shops </a></div>
            </div>
        </div>
        <?php if (isset($banner_ads_middle_side_right) && !empty($banner_ads_middle_side_right)): ?>
            <div class="aside-bottom">
                <?php foreach ($banner_ads_middle_side_right as $val): ?>
                    <?php if ($val['ad_type'] == 'Sidebar Right'): ?>
                        <div class="img-wrapper">
                            <img class="aside-bottom-img" src="<?= $val['web_image'] ?>"/>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</home>
<?php if (count($topdeals) > 0): ?>
    <carousal class="carousal-overall-container">
        <p class="deal-text"><?= __('Top Deal Items') ?></p>
        <div class="carousel-controls">
            <button class="carousel-control left" onclick="moveLeftCarousel()">🡠</button>
            <button class="carousel-control right" onclick="moveRightCarousel()">🡢</button>
        </div>
        <div class="parent-carousel">
            <div class="carousel">
                <div class="carousel-inner">
                    <?php foreach ($topdeals as $val): ?>
                        <div class="card">
                            <?php if (!empty($val['web_image'])): ?>
                                <div class="img-wrapper">
                                    <img src="<?= $val['web_image'] ?>" class="top-deal-image"/>
                                </div>
                            <?php else: ?>
                                <div class="img-wrapper">
                                    <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="top-deal-image"
                                     alt="No Image Available"/>
                                </div>
                            <?php endif; ?>

                            <div class="top-deal-heading"><?= h($val['offer_name']) ?></div>
                            <!--                            <div class="top-deal-subheading">UP TO -->
                            <?php //= h($val['discount']) ?><!-- OFF</div>-->
                            <div class="top-deal-subheading"><?= h($val['offer_description']) ?></div>
                        </div>
                    <?php endforeach; ?>

                </div>
            </div>
        </div>
    </carousal>
<?php endif; ?>








<?php if (isset($widgets['best_selling']) && count($widgets['best_selling']) > 0): ?>
<div class="custom-carousel-container">
    <p class="promo-text">
        <?php if (isset($widgets['best_selling'][0]['widget']['title']) && !empty($widgets['best_selling'][0]['widget']['title'])): ?>
            <?= trim($widgets['best_selling'][0]['widget']['title']); ?>
        <?php else: ?>
            <?= __('Top Selling Items') ?>
        <?php endif; ?>
    </p>



        <div class="custom-carousel-container">
            <div class="custom-carousel-controls" id="features-cat-slider-arrow">
                <button class="carousel-button left-btn" onclick="moveLeftCustomCarouselagain()">🡠</button>
                <button class="carousel-button right-btn" onclick="moveRightCustomCarouselagain()">🡢</button>
            </div>
            <div class="custom-parent-carousel clone-custom-parent-carousel">
                <div class="custom-carousel">
                    <div class="custom-carousel-inner-class">
                        <?php if (isset($widgets['best_selling'][0]['products'])): ?>





            <?php foreach ($widgets['best_selling'][0]['products'] as $val): ?>
                <div class="custom-card">
                    <div class="custom-box-2">

                        <?php if ($val->whishlist): ?>
                            <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                 data-product-id="<?= $val->id ?>"><span
                                    class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                            </div>
                        <?php else: ?>
                            <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                 data-product-id="<?= $val->id ?>">
                                 <span class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div style="snd-crsl-img" id="snd-crsl-img">
                        <?php if (!empty($val['product_image'])): ?>
                            <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>"><img src="<?= $val['product_image'] ?>" class="snd-crsl-img" alt="Product Image" /></a>
                        <?php else: ?>
                    <img src="<?= $this->Url->webroot('assets/no-image.png') ?>"
                         alt="Product Image"/>
                        <div>
                            <?php endif; ?>
                            <?php
                            $rating = $val['rating'];
                            $fullStars = floor($rating);
                            $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                            $emptyStars = 5 - $fullStars - $halfStar;
                            $starsHtml = str_repeat('★', $fullStars); // Full stars
                            if ($halfStar) {
                                $starsHtml .= '★☆'; // Half star
                            }
                            $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                            ?>

                            <div class="custom-card-star">
                                <?= $starsHtml ?> <span
                                    class="custom-card-rating"><?= number_format($rating, 1) ?></span>

                            </div>

                            <div class="custom-card-name card-t-name">
                                <a class="custom-card-name" href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>"> <?= $val['name'] ?> </a>
                             </div>
                             <div class="custom-card-subname"><?= __("Reference: ") ?> <?= $val['reference_name'] ?? "" ?></div>


                            <div class="custom-card-price"><?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                            <button class="custom-card-add-to-cart-button add-to-cart"
                                    data-product-id="<?= $val['id'] ?>">
                                <span class="custom-card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                                <span class="custom-card-add-to-cart">ADD TO CART</span>
                            </button>
                        </div>

                            <span class="strikethrough-text"> <?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                            <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span>
                        </div>
                        </div>
                <?php endforeach; ?>





                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

<?php endif; ?>




        <div class="custom-parent-carousel custom-parent-carousel-cl">


            <?php if (isset($widgets['new_arrival'][0]['products'])): ?>
                <div class="my-carousel-container">
                    <p class="New-Arrivals-text" id="New-Arrivals-text">
                        <?php if (isset($widgets['new_arrival'][0]['widget']['title']) && !empty($widgets['new_arrival'][0]['widget']['title'])): ?>
                            <?= trim($widgets['new_arrival'][0]['widget']['title']); ?>
                        <?php else: ?>
                            <?= __("New Arrivals") ?>
                        <?php endif; ?>
                    </p>
                    <div class="my-carousel-controls">
                        <button class="carousel-button left-btn" onclick="moveLeftMyCarouselMy()">🡠</button>
                        <button class="carousel-button right-btn" onclick="moveRightMyCarouselMy()">🡢</button>
                    </div>

                    <div class="my-parent-carousel">
                        <div class="my-carousel">

                            <div class="my-carousel-inner-div">

                                <?php foreach ($widgets['new_arrival'][0]['products'] as $val): ?>
                                    <div class="custom-card-div">
                                        <div class="custom-box-2">

                                            <?php if ($val->whishlist): ?>
                                                <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                <span class="snd-crsl-img">
                                <?php if (!empty($val['product_image'])): ?>
                                    <div class="img-wrapper">
                                        <img src="<?= $val['product_image'] ?>" class="snd-crsl-img" alt="Product Image"/>
                                    </div>
                                <?php else: ?>
                                    <div class="img-wrapper">
                                        <img src="<?= $this->Url->webroot('assets/no-image.png') ?>" class="snd-crsl-img"
                                        alt="Product Image"/>
                                    </div>
                                </span>
                                        <?php endif; ?>
                                        <?php
                                        $rating = $val['rating'];
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                        $starsHtml = str_repeat('★', $fullStars); // Full stars
                                        if ($halfStar) {
                                            $starsHtml .= '★☆'; // Half star
                                        }
                                        $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                        ?>
                                        <div class="custom-card-star">
                                            <?= $starsHtml ?> <span
                                                class="custom-card-rating"><?= number_format($rating, 1) ?></span>
                                        </div>
                                        <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val->url_key]) ?>">
                                            <div class="custom-card-name card-t-name">
                                                <?= $val['name'] ?>

                                            </div>
                                            <div class="custom-card-subname"><?= __("Reference: ") ?> <?= $val['reference_name'] ?? "" ?></div>

                                        </a>

                                        <div class="custom-card-price"><?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                        <button class="custom-card-add-to-cart-button  add-to-cart"
                                                data-product-id="<?= $val['id'] ?>">
                                            <span class="custom-card-cart-icon"><i
                                                    class="fas fa-cart-arrow-down"></i></span>
                                            <span class="custom-card-add-to-cart">ADD TO CART</span>
                                        </button></div>

                                        <span class="strikethrough-text"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                        <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span>
                                        <div class="custom-box">
                                            <?= __("NEW") ?>
                                        </div>

                                    </div>
                                <?php endforeach; ?>


                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>





        </div>

            <?php if (isset($banner_ads_middle) && !empty($banner_ads_middle)): ?>
                <div class="parent-landscape-slider">
                    <div class="landscper-slider-button-container">
                        <div class="landscape-slider">
                            <div class="landscape-images">
                                <?php foreach ($banner_ads_middle as $val): ?>
                                    <?php if ($val['ad_type'] == 'Middle'): ?>
                                        <img class="landscape-images-img" src="<?= $val['web_image'] ?>"/>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <button class="prev" onclick="plusSlides(-1)">🡠</button>
                        <button class="next" onclick="plusSlides(1)">🡢</button>
                    </div>
                </div>
            <?php endif; ?>

      
                <?php   if (isset($widgets['deal']) && count($widgets['deal']) > 0): ?>
            <div class="deal-day">
                    <p class="Deal-of-the-day"><?php echo $widgets['deal'][0]['widget']['title']; ?>
                        <p class="deal-of-day-timer">Deals end in: <span id="timer"></span></p>
                    </p>

                    <span>

                        <?php
                            // Current date and time
                            $currentDate = new DateTime();

                            // Replace this with your dynamic end date from the array
                            $endDate = new DateTime(date('Y-m-d') . ' 23:59:59'); // DateTime($widgets['deal'][0]['widget']['end_date']);

                            // Check if the deal is still active
                            if ($endDate > $currentDate) {
                            // Calculate the remaining time in seconds
                            $remainingSeconds = $endDate->getTimestamp() - $currentDate->getTimestamp();
                        ?>

                    <script>
                        // JavaScript Countdown Timer
                        let remainingTime = <?php echo $remainingSeconds; ?>;

                        function updateTimer() {
                            if (remainingTime <= 0) {
                                document.getElementById('timer').innerText = "Deal has ended.";
                                return;
                            }

                            // Calculate hours, minutes, and seconds
                            const hours = Math.floor(remainingTime / 3600);
                            const minutes = Math.floor((remainingTime % 3600) / 60);
                            const seconds = remainingTime % 60;

                            // Display the time in HH:MM:SS format
                            document.getElementById('timer').innerText =
                                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                            // Decrement the remaining time
                            remainingTime--;
                        }

                        // Initialize the timer
                        updateTimer();
                        setInterval(updateTimer, 1000);
                    </script>
                <?php
                } else {
                    // If the deal has already ended
                    echo "<p>". __('The deal has ended.') . "</p>";
                }
                ?>
                </span>

                <a class="browse-all-product" href="/deals">
                    <?= __('Browse All Product') ?>  <span class="cat-aero">🡢</span>
                </a>
                <div class="prnt-pt-ad">
                    <div class="img-wrapper">
                        <?php if (isset($banner_ads_middle_sidebar_left) && !empty($banner_ads_middle_sidebar_left)): ?>
                            <?php foreach ($banner_ads_middle_sidebar_left as $val): ?>
                                <?php if ($val['ad_type'] == 'Sidebar Left'): ?>
                                    <img src="<?= $val['web_image'] ?>" class="pt-ad"/>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    <?php
                    // print_r($widgets['deal'][0]['widget']['products']); die;
                    // Split the products array into two parts for display in two columns
                    $firstColumn = array_slice($widgets['deal'][0]['products'], 0, 4);
                    $secondColumn = array_slice($widgets['deal'][0]['products'], 4, 4);
                    ?>

                    <div class="deal-of-day-carts1-2">
                        <!-- First Column -->
                        <div class="deal-of-day-carts">
                            <?php foreach ($firstColumn as $product): ?>
                                <div class="custom-card-1">

                                    <div class="like-box-home">
                                        <?php if ($product->whishlist): ?>
                                            <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                                    data-product-id="<?= $product->id ?>"><span
                                                    class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                                    data-product-id="<?= $product->id ?>"><span
                                                    class="p-v-p-item-description-add-to-wishlist-heart"><img src="/assets/heart-nobackground.png" class="wishlist"></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <img src="<?= $product['product_image'] ?>" class="snd-crsl-img-1"/>
                                    <div class="hr-container">
                                        <div class="hr" style="width: 50%;background: #FF3B3B;height: 100%;"></div>
                                    </div>
                                    <div class="custom-card-star-1">

                                        <?php
                                        $rating = $product['rating'];
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                        $starsHtml = str_repeat('★', $fullStars); // Full stars
                                        if ($halfStar) {
                                            $starsHtml .= '★☆'; // Half star
                                        }
                                        $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                        ?>
                                        <div class="custom-card-star">
                                            <?= $starsHtml ?> <span
                                                class="custom-card-rating-1"><?= number_format($rating, 1) ?></span>
                                        </div>
                                    </div>
                                    <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $product->url_key]) ?>">
                                        <div class="custom-card-name-1">
                                            <?= $product['name'] ?>
                                        </div>
                                        <div class="custom-card-subname">Reference: <?= $product['reference_name'] ?? "" ?></div>
                                        <!-- <div class="custom-card-subname-1"><?= $product['reference_name'] ?? "" ?></div> -->
                                    </a>

                                    <div>
                                        <div class="custom-card-price-1"><?= $this->Price->setPriceFormat($product['promotion_price']) ?>
                                        <button class="custom-card-add-to-cart-button-1  add-to-cart"
                                                data-product-id="<?= $product->id ?>">
                                            <span class="custom-card-cart-icon-1"><i class="fas fa-cart-arrow-down"></i></span>
                                            <span class="custom-card-add-to-cart-1">ADD TO CART</span>
                                        </button></div>

                                    </div>
                                    <span class="strikethrough-text-1"><?= $this->Price->setPriceFormat($product['sales_price']) ?></span>
                                    <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $product['discount'] ?></span>% 0ff</span>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Second Column -->
                        <div class="deal-of-day-carts-2">
                            <?php foreach ($secondColumn as $product): ?>
                                <div class="custom-card-1">
                                    <div class="like-box-home">
                                        <?php if ($product->whishlist): ?>
                                            <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                                    data-product-id="<?= $product->id ?>"><span
                                                    class="p-v-p-item-description-add-to-wishlist-heart"><img src="/assets/heart-background.png" class="wishlist"> </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                                    data-product-id="<?= $product->id ?>"><span
                                                    class="p-v-p-item-description-add-to-wishlist-heart"><img src="/assets/heart-nobackground.png" class="wishlist">  </span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <img src="<?= $product['product_image'] ?>" style="height: 220px;" class="snd-crsl-img-1"/>
                                    <div class="hr-container">
                                        <div class="hr" style="width: 50%;background: #FF3B3B;height: 100%;"></div>
                                    </div>
                                    <div class="custom-card-star-1">

                                        <?php
                                        $rating = $product['rating'];
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                        $starsHtml = str_repeat('★', $fullStars); // Full stars
                                        if ($halfStar) {
                                            $starsHtml .= '★☆'; // Half star
                                        }
                                        $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                        ?>
                                        <div class="custom-card-star">
                                            <?= $starsHtml ?> <span
                                                class="custom-card-rating-1"><?= number_format($rating, 1) ?></span>
                                        </div>

                                    </div>
                                    <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $product['url_key']]) ?>">
                                        <div class="custom-card-name-1">
                                        <?= $product['name'] ?>
                                            <!-- <div class="custom-card-subname">Reference: </div> -->
                                        </div>
                                    </a>
                                    <div class="custom-card-subname-1">Reference: <?= $product['reference_name'] ?? "" ?></div>
                                    <div>
                                        <div class="custom-card-price-1"><?= $this->Price->setPriceFormat($product['promotion_price']) ?>
                                        <button class="custom-card-add-to-cart-button-1  add-to-cart"
                                                data-product-id="<?= $product['id'] ?>">
                                            <span class="custom-card-cart-icon-1"><i class="fas fa-cart-arrow-down"></i></span>
                                            <span class="custom-card-add-to-cart-1">ADD TO CART</span>
                                        </button>
                                        </div>
                                    </div>
                                    <span class="strikethrough-text-1"><?= $this->Price->setPriceFormat($product['sales_price']) ?></span>
                                    <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $product['discount'] ?></span>% 0ff</span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                </div>
         </div>
            <?php endif; ?>
       
        
        <?php if (isset($widgets['featured']) && is_array($widgets['featured']) && count($widgets['featured']) > 0): ?>

            <p class="Special-Offers" id="features-cat">
                <?php if (isset($widgets['featured'][0]['widget']['title']) && !empty($widgets['featured'][0]['widget']['title'])): ?>
                    <?= trim($widgets['featured'][0]['widget']['title']); ?>
                <?php else: ?>
                    <?= __('Featured Categories') ?>
                <?php endif; ?>
            </p>

            <div class="custom-carousel-container">
                <div class="custom-carousel-controls" id="features-cat-slider-arrow">
                    <button class="carousel-button left-btn" onclick="moveLeftCustomCarouselagain2()">🡠</button>
                    <button class="carousel-button right-btn" onclick="moveRightCustomCarouselagain2()">🡢</button>
                </div>
                <div class="custom-parent-carousel clone-custom-parent-carousel">
                    <div class="custom-carousel">
                        <div class="custom-carousel-inner-class">
                            <?php if (isset($widgets['featured'][0]['products'])): ?>
                                <?php foreach ($widgets['featured'][0]['products'] as $val): ?>
                                    <div class="custom-card">
                                        <div class="like-box-home">

                                            <?php if ($val->whishlist): ?>
                                                <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn"
                                                    data-product-id="<?= $val->id ?>"><span
                                                        class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                                                </div>
                                            <?php endif; ?>

                                        </div>
                                        <?php if (!empty($val['product_image'])): ?>
                                            <div class="img-container">
                                                <img src="<?= $val['product_image'] ?>" class="snd-crsl-img"
                                                alt="Product Image"/>
                                            </div>
                                        <?php else: ?>
                                            <div class="img-container">
                                                <img src="<?= $this->Url->webroot('assets/no-image.png') ?>"
                                                class="snd-crsl-img"
                                                alt="Product Image"/>
                                            </div>
                                        <?php endif; ?>
                                        <?php
                                        $rating = $val['rating'];
                                        $fullStars = floor($rating);
                                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                                        $emptyStars = 5 - $fullStars - $halfStar;
                                        $starsHtml = str_repeat('★', $fullStars); // Full stars
                                        if ($halfStar) {
                                            $starsHtml .= '★☆'; // Half star
                                        }
                                        $starsHtml .= str_repeat('☆', $emptyStars); // Empty stars
                                        ?>
                                        <div class="custom-card-star">
                                            <?= $starsHtml ?> <span
                                                class="custom-card-rating"><?= number_format($rating, 1) ?></span>
                                        </div>
                                        <div class="custom-product-name">
                                            <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val['url_key']]) ?>">
                                                <div class="custom-card-name card-t-name">
                                                    <?= $val['name'] ?>

                                                </div>
                                            </a>
                                            <div class="custom-card-subname"><?= __("Reference: ") ?> <?= $val['reference_name'] ?? "" ?></div>
                                            <!-- <div class="custom-card-subname"><?= $val['reference_name'] ?? "" ?></div> -->
                                        </div>
                                        <div class="custom-card-price"><?= $this->Price->setPriceFormat($val['promotion_price']) ?>
                                    <button class="custom-card-add-to-cart-button  add-to-cart"
                                                data-product-id="<?= $val['id'] ?>">
                                            <span class="custom-card-cart-icon"><i
                                                    class="fas fa-cart-arrow-down"></i></span>
                                            <span class="custom-card-add-to-cart">ADD TO CART</span>
                                        </button></div>

                                        <span class="strikethrough-text"><?= $this->Price->setPriceFormat($val['sales_price']) ?></span>
                                        <span class="p-v-p-item-description-image-description-price-offer"><span class="ax-sale-off"><?= $val['discount'] ?></span>% 0ff</span>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        <?php endif; ?>


        <?php if (isset($BrandsList) && !empty($BrandsList)): ?>
            <div class="Our-Brands">Our Brands</div>

            <div class="custom-carousel-controls">
                <button class="carousel-button brands-left-btn">🡠</button>
                <button class="carousel-button brands-right-btn">🡢</button>
            </div>

            <div class="brnds-container">
                <div class="brnds-container-iner">
                <?php foreach ($BrandsList as $val): ?>
                    <a href="<?= '/website/product-brand-list/' . $val['url_key'] ?>">
                        <img
                            src="<?= !empty($val['brand_logo']) ? $val['brand_logo'] : $this->Url->webroot('assets/no-image.png') ?>"
                            class="brands" />
                    </a>
                <?php endforeach; ?>
                </div>
            </div>

        <?php endif; ?>

        <?php if (isset($userId) && $userId !== null): ?>
            <div class="custom-carousel-container" style="margin-top: 3%;">
                <p class="Special-Offers">Recently Viewed Products</p>
                <div class="custom-carousel-controls Recently-reviewed-for-mob">
                    <button class="carousel-button left-btn-s" onclick="slideLeftRecentlyViewed()">🡠</button>
                    <button class="carousel-button right-btn-s" onclick="slideRightRecentlyViewed()">🡢</button>
                </div>
                <div class="custom-parent-carousel clone-custom-parent-carousel">
                    <div class="custom-carousel">
                        <div class="custom-carousel-inner" id="recently-viewed-slider">
                            <?= $this->RecentViewProduct->renderSlider($userId) ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($banner_ads_middle_above_footer) && !empty($banner_ads_middle_above_footer)): ?>
            <div class="ad-img">
                <?php foreach ($banner_ads_middle_above_footer as $val): ?>
                    <?php if ($val['ad_type'] == 'Above Footer'): ?>
                        <img src="<?= $val['web_image'] ?>" class="ad-img-1"/>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <p class="What-Our-Cli"><?= __("What Our Client Say About Us") ?></p>

        <div class="my-carousel-controls-s">
            <button class="carousel-button left-btn-s" onclick="slideLeftMyCarousel()">🡠</button>
            <button class="carousel-button right-btn-s" onclick="moveRightMyCarousel()">🡢</button>
        </div>

        <div class="my-parent-carousel-s">
            <div class="my-carousel-s">

                <div class="my-carousel-inner-s">

                    <?php foreach ($testimonials as $val): ?>
                        <div class="my-card-s">
                                    <div class="cards-inner-text">
                                        <img src="<?= $this->Url->webroot('/assets/profile-icon.png') ?>" class="dp-img"/>
                                        <p class="dp-name"><?= $val['name'] ?>
                                            <span class="star-rating">
                                <span class="star">&#9733;</span>
                                <span class="rating">
                                    <?= strlen($val['rating']) > 30 ? substr($val['rating'], 0, 30) : $val['rating'] ?>
                                </span>
                            </span>
                                        </p>
                                        <p class="comment clone-comment">  <?= strlen($val['comment']) > 200 ? substr($val['comment'], 0, 200) : $val['comment'] ?></p>
                                    </div>

                        </div>
                    <?php endforeach; ?>




                </div>



            </div>
        </div>

        <div class="ques-other-parent">
            <div class="ques">
                <?php foreach ($getFaqsList as $val): ?>
                    <div class="faq-item">
                        <p class="bab-ques-q"><?= $val['title'] ?> <span class="plus">+</span></p>
                        <div class="faq-content">
                            <p><?= $val['content'] ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="other">
                <p class="bab-ques-w">How we can help you?</p>
                <p class="bab-ques-b">If you have any questions or queries, direct connect with us!
                    <br />
                <a href="tel:<?= h($this->SiteSettings->getSettings()->customer_support_no) ?>" style="text-decoration: none; color: inherit;">
                    <i class="fas fa-mobile-alt"></i> <?= h($this->SiteSettings->getSettings()->customer_support_no) ?>
                </a>
                </p>
            </div>
            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const faqItems = document.querySelectorAll(".faq-item");

                    faqItems.forEach(item => {
                        const question = item.querySelector(".bab-ques-q");
                        question.addEventListener("click", function () {
                            item.classList.toggle("active");

                            const content = item.querySelector(".faq-content");
                            if (item.classList.contains("active")) {
                                content.style.maxHeight = content.scrollHeight + "px";
                            } else {
                                content.style.maxHeight = null;
                            }

                            const plus = question.querySelector(".plus");
                            if (item.classList.contains("active")) {
                                plus.textContent = "-";
                            } else {
                                plus.textContent = "+";
                            }
                        });
                    });
                });

                let currentIndex = 0;

                function moveLeftCustomCarousel() {
                const carouselInner = document.querySelector(".custom-carousel-inner");
                const cards = document.querySelectorAll(".custom-card");
                const cardWidth = cards[0].offsetWidth; // Width of one card including margin
                const totalCards = cards.length;

                // Move left and wrap around to the last card if at the beginning
                if (currentIndex === 0) {
                    currentIndex = totalCards - 1; // Go to the last card
                } else {
                    currentIndex--;
                }

                carouselInner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;
                }

                function moveRightCustomCarousel() {
                const carouselInner = document.querySelector(".custom-carousel-inner");
                const cards = document.querySelectorAll(".custom-card");
                const cardWidth = cards[0].offsetWidth; // Width of one card including margin
                const totalCards = cards.length;

                // Move right and wrap around to the first card if at the end
                if (currentIndex === totalCards - 1) {
                    currentIndex = 0; // Go to the first card
                } else {
                    currentIndex++;
                }

                carouselInner.style.transform = `translateX(-${currentIndex * cardWidth}px)`;
                }

                document.addEventListener("DOMContentLoaded", function () {
                    const container = document.querySelector(".brnds-container");
                    const leftButton = document.querySelector(".left-btn");
                    const rightButton = document.querySelector(".right-btn");

                    // Define the amount of scroll
                    const scrollAmount = 200;

                    rightButton.addEventListener("click", () => {
                        container.scrollBy({ left: scrollAmount, behavior: "smooth" });
                    });

                    leftButton.addEventListener("click", () => {
                        container.scrollBy({ left: -scrollAmount, behavior: "smooth" });
                    });
                });

            </script>

            <script>
                document.addEventListener("DOMContentLoaded", function () {
                const container = document.querySelector(".brnds-container-iner");
                const leftBtn = document.querySelector(".brands-left-btn");
                const rightBtn = document.querySelector(".brands-right-btn");

                const scrollAmount = 300; // Adjust the scroll step size

                    rightBtn.addEventListener("click", function () {
                        container.scrollBy({ left: scrollAmount, behavior: "smooth" });
                    });

                    leftBtn.addEventListener("click", function () {
                        container.scrollBy({ left: -scrollAmount, behavior: "smooth" });
                    });
                });
            </script>


            <script>
                let axIndex = 0;
                const axSlides = document.querySelector(".ax-slides");
                const axTotalSlides = document.querySelectorAll(".ax-slide").length;
                const axPrevBtn = document.querySelector(".ax-prev");
                const axNextBtn = document.querySelector(".ax-next");

                if (axPrevBtn && axNextBtn) {
                    axNextBtn.addEventListener("click", () => {
                        axIndex = (axIndex + 1) % axTotalSlides;
                        axUpdateSlide();
                    });

                    axPrevBtn.addEventListener("click", () => {
                        axIndex = (axIndex - 1 + axTotalSlides) % axTotalSlides;
                        axUpdateSlide();
                    });

                    function axUpdateSlide() {
                        axSlides.style.transform = `translateX(-${axIndex * 100}%)`;
                    }

                    setInterval(() => {
                        axIndex = (axIndex + 1) % axTotalSlides;
                        axUpdateSlide();
                    }, 3000);
                }
            </script>

    </div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const recentlyViewedContainer = document.querySelector('.recently-viewed-inner');
        const prevBtn = document.querySelector('.recently-viewed-prev');
        const nextBtn = document.querySelector('.recently-viewed-next');

        if (recentlyViewedContainer && prevBtn && nextBtn) {
            let scrollAmount = 0;
            const cardWidth = 280; // Width of each card including margin
            const scrollSpeed = cardWidth;

            prevBtn.addEventListener('click', function() {
                scrollAmount = Math.max(scrollAmount - scrollSpeed, 0);
                smoothScroll(recentlyViewedContainer, scrollAmount);
            });

            nextBtn.addEventListener('click', function() {
                const maxScroll = recentlyViewedContainer.scrollWidth - recentlyViewedContainer.clientWidth;
                scrollAmount = Math.min(scrollAmount + scrollSpeed, maxScroll);
                smoothScroll(recentlyViewedContainer, scrollAmount);
            });
        }
    });

    function smoothScroll(element, target) {
        element.style.scrollBehavior = 'smooth';
        element.scrollLeft = target;
    }
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure the slider is initialized after the DOM is fully loaded
        initRecentlyViewedSlider();
    });

    function initRecentlyViewedSlider() {
        // Try different selectors to find the container, prioritizing the ID
        const container = document.getElementById('recently-viewed-slider') ||
                         document.querySelector('.custom-carousel-inner') ||
                         document.querySelector('.clone-custom-parent-carousel .custom-carousel-inner');

        if (!container) {
            console.log('Recently Viewed Slider container not found');
            return;
        }

        const cards = container.querySelectorAll('.custom-card, .custom-card-recent');

        if (cards.length > 0) {
            // Set initial scroll position
            container.scrollLeft = 0;

            // Calculate the width of each card including margins
            const cardWidth = cards[0].offsetWidth +
                parseInt(window.getComputedStyle(cards[0]).marginLeft || 0) +
                parseInt(window.getComputedStyle(cards[0]).marginRight || 0);

            // Set the scroll amount to the width of one card
            window.recentlyViewedScrollAmount = cardWidth || 280;

            // Store the container reference for later use
            window.recentlyViewedContainer = container;

            console.log('Recently Viewed Slider initialized with card width:', window.recentlyViewedScrollAmount);
        } else {
            console.log('Recently Viewed Slider cards not found');
        }
    }

    function slideLeftRecentlyViewed() {
        // Use the stored container reference or try to find it again, prioritizing the ID
        const container = window.recentlyViewedContainer ||
                         document.getElementById('recently-viewed-slider') ||
                         document.querySelector('.custom-carousel-inner') ||
                         document.querySelector('.clone-custom-parent-carousel .custom-carousel-inner');

        if (container) {
            // Use the calculated scroll amount or default to 280px
            const scrollAmount = window.recentlyViewedScrollAmount || 280;

            // Scroll left by the width of one card
            container.scrollBy({
                left: -scrollAmount,
                behavior: 'smooth'
            });

            console.log('Sliding left by', scrollAmount, 'pixels');
        } else {
            console.log('Container not found for slideLeftRecentlyViewed');
        }
    }

    function slideRightRecentlyViewed() {
        // Use the stored container reference or try to find it again, prioritizing the ID
        const container = window.recentlyViewedContainer ||
                         document.getElementById('recently-viewed-slider') ||
                         document.querySelector('.custom-carousel-inner') ||
                         document.querySelector('.clone-custom-parent-carousel .custom-carousel-inner');

        if (container) {
            // Use the calculated scroll amount or default to 280px
            const scrollAmount = window.recentlyViewedScrollAmount || 280;

            // Scroll right by the width of one card
            container.scrollBy({
                left: scrollAmount,
                behavior: 'smooth'
            });

            console.log('Sliding right by', scrollAmount, 'pixels');
        } else {
            console.log('Container not found for slideRightRecentlyViewed');
        }
    }
</script>

<style>
    /* Ensure the carousel container has proper overflow handling */
    .custom-carousel-inner {
        display: flex;
        /* overflow-x: auto;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch; */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .custom-carousel-inner::-webkit-scrollbar {
        display: none;
    }
    .Download-The-App, .Payment-Methods {
        margin-top: 0px;
    }
</style>