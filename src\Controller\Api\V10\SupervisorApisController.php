<?php
declare(strict_types=1);

//namespace App\Controller;
namespace App\Controller\Api\V10;

//use App\Controller\AppController;
use App\Controller\Api\V10\AppController;
use Cake\Event\Event;
use Cake\Event\EventInterface;
use Cake\Core\Configure;
use Cake\Core\Configure\Engine\PhpConfig;
use Cake\Core\Exception\Exception;
use Cake\I18n\Time;
use DateTime;
//use Cake\Http\Response;
//use Cake\Http\Exception\BadRequestException;
//use Cake\Http\Exception\NotFoundException;
use Mpdf\Mpdf;
use Cake\View\JsonView;
use Cake\Utility\Security;
use Cake\Routing\Router;
use Cake\I18n\FrozenTime;
use Cake\I18n\FrozenDate;
use Cake\Database\Expression\IdentifierExpression;
use Cake\ORM\TableRegistry;
use Cake\Database\Expression\QueryExpression;

class SupervisorApisController extends AppController
{
    // Declare the property
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $CartItemAttributes;
    protected $Municipalities;
    protected $Roles;
    protected $ShowroomExpenses;
    protected $Zones;
    protected $ShowroomUsers;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $ProductStocks;
    protected $UserActions;
    protected $ShowroomExpenseCategories;
    protected $SupportCategories;
    protected $SupportTickets;
    protected $SupportTicketUpdates;
    protected $SupportTicketImages;
    protected $Notifications;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrdersItems;
    protected $CashDeskClosures;
    protected $CustomerGroupMappings;
    protected $ProductCategories;
    protected $Warehouses;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $ProductAttributes;
    protected $ProductVariants;
    protected $Drivers;
    protected $SupplierProducts;
    protected $Suppliers;
    protected $SupplierPayment;
    protected $CashHandovers;
    
    public function initialize(): void
    {
        parent::initialize(); 

        $this->Users = $this->fetchTable('Users');
        $this->Cities = $this->fetchTable('Cities'); 
        $this->Municipalities = $this->fetchTable('Municipalities'); 
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Offers = $this->fetchTable('Offers'); 
        $this->Roles = $this->fetchTable('Roles');
        $this->Customers = $this->fetchTable('Customers');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');        
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems'); 
        $this->Transactions = $this->fetchTable('Transactions');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->ShowroomExpenses = $this->fetchTable('ShowroomExpenses');        
        $this->Zones = $this->fetchTable('Zones');
        $this->Showrooms = $this->fetchTable('Showrooms'); 
        $this->ShowroomUsers = $this->fetchTable('ShowroomUsers'); 
        $this->StockMovements = $this->fetchTable('StockMovements'); 
        $this->StockMovementItems = $this->fetchTable('StockMovementItems'); 
        $this->ProductStocks = $this->fetchTable('ProductStocks');        
        $this->UserActions = $this->fetchTable('UserActions');
        $this->Products = $this->fetchTable('Products');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->ShowroomExpenseCategories = $this->fetchTable('ShowroomExpenseCategories');
        $this->SupportCategories = $this->fetchTable('SupportCategories');        
        $this->SupportTickets = $this->fetchTable('SupportTickets');
        $this->SupportTicketUpdates = $this->fetchTable('SupportTicketUpdates');
        $this->SupportTicketImages = $this->fetchTable('SupportTicketImages'); 
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories'); 
        $this->DeliveryCharges = $this->fetchTable('DeliveryCharges');
        $this->Loyalty = $this->fetchTable('Loyalty'); 
        $this->Notifications = $this->fetchTable('Notifications'); 
        $this->SupplierPurchaseOrders = $this->fetchTable('SupplierPurchaseOrders');
        $this->SupplierPurchaseOrdersItems = $this->fetchTable('SupplierPurchaseOrdersItems');
        $this->CashDeskClosures = $this->fetchTable('CashDeskClosures'); 
        $this->CustomerGroupMappings = $this->fetchTable('CustomerGroupMappings');
        $this->ProductCategories = $this->fetchTable('ProductCategories'); 
        $this->Warehouses = $this->fetchTable('Warehouses'); 
        $this->StockRequests = $this->fetchTable('StockRequests'); 
        $this->StockRequestItems = $this->fetchTable('StockRequestItems'); 
        $this->ProductAttributes = $this->fetchTable('ProductAttributes'); 
        $this->ProductVariants = $this->fetchTable('ProductVariants'); 
        $this->Drivers = $this->fetchTable('Drivers'); 
        $this->SupplierProducts = $this->fetchTable('SupplierProducts'); 
        $this->Suppliers = $this->fetchTable('Suppliers'); 
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->SupplierPayment = $this->fetchTable('SupplierPayment');
        $this->CashHandovers = $this->fetchTable('CashHandovers');        
               
        $this->loadComponent('Stock');
        $this->loadComponent('Global');
        $this->loadComponent('Mtn');
        $this->loadComponent('Wave');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('WebsiteFunction');

        $this->Authentication->addUnauthenticatedActions(['test', 'listCity', 'listShowroom', 'listMunicipality', 'login', 'forgotPassword', 'resetPassword']);
    }

    public function beforeFilter(\Cake\Event\EventInterface $event)
    {
        parent::beforeFilter($event);
        //$this->response = $this->response->withType('application/json');
    }

    public function viewClasses(): array
    {
        return [JsonView::class];
    }

    //S
    public function test() {

        /*$warehouse_id=1; $movement_date = date('Y-m-d'); $reference_type='stock_request'; $referenceID=1;
        $stock_items[0]['product_id'] = 41;
        $stock_items[0]['quantity'] = 2;
        $stock_items[1]['product_id'] = 42;
        $stock_items[1]['quantity'] = 100;
       
        $result = $this->Stock->addWarehouseInStock ($warehouse_id, $movement_date, $reference_type, $referenceID, $stock_items);*/
        //$order_no = '098765421'; $amount = '100'; $customer_phone = '46733123455'; $customer_name = "Siddharth"; $customer_email = "<EMAIL>";
        //$result = $this->Mtn->initiatePayment($order_no, $amount, $customer_phone);
        //echo $result['status'];
        //$result = $this->Mtn->checkPaymentStatus('49987bee-cd8a-459c-a3ee-0a563938ed82');
        //$result = $this->Wave->createCheckoutSession($order_no, $amount, $customer_name, $customer_email);
        //$result = $this->Wave->retrieveCheckout('cos-1t2pdzcdr2294');
        //$result = $this->Wave->webhookTest();
        $mobile_no = "+919916699357";
        $message_text = "Hi test by sid";
        $result = $this->Global->sendWhatsAppMessage($mobile_no, $message_text);
        echo "<pre>"; print_r($result); exit;
        //$this->set(['result' => $result]);
        //$this->viewBuilder()->setOption('serialize', ['result']);

    }

    function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');

        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;

        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }

        return $now->format('Y-m-d h:i:s');
    }

    //S
    public function getProductStock() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'] ?: null;
            $productId = $data['product_id'];
            $variantId = $data['variant_id'] ?: null;
            $attributeId = $data['attribute_id'] ?: null;

            //
            $product_stock = $this->Stock->getProductStock($showroomId, $productId, $variantId, $attributeId);

            $result = ['status' => __('success'), 'data' => ['product_stock'=>$product_stock] ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function reserveStock() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'] ?: null;
            $productId = $data['product_id'] ?: null;
            $productVarId = $data['variant_id'] ?: null;
            $attributeId = $data['attribute_id'] ?: null;
            $prevquantity = $data['prevquantity'];
            $current_quantity = $data['current_quantity'];

            //
            $reserve_stock = $this->Stock->reserveStock($showroomId, $productId, $productVarId, $attributeId, $prevquantity, $current_quantity);
            

            $result = ['status' => __('success'), 'data' => ['reserve_stock'=>$reserve_stock] ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function outgoingStock() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $orderId = $data['order_id'];

            //
            $mark_outgoing_stock = $this->Stock->outgoingStock($orderId);

            $result = ['status' => __('success'), 'message' => __($mark_outgoing_stock) ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function clearStock() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'] ?: null;
            $productId = $data['product_id'] ?: null;
            $productVarId = $data['variant_id'] ?: null;
            $attributeId = $data['attribute_id'] ?: null;
            $current_quantity = $data['current_quantity'];

            //
            $clear_stock = $this->Stock->clearStock($showroomId, $productId, $productVarId, $attributeId, $current_quantity);

            $result = ['status' => __('success'), 'data' => ['clear_stock'=>$clear_stock] ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function paymentMethods()
    {

        if ($this->request->is('get')) {

            $methods = $this->PaymentMethods->listMethods();

            $result = [
                'status' => __('success'),
                'code' => 200,
                'data' => $methods,
                'message' => __('Payment method listing successfully')
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S order download invoice
    public function viewInvoice($id = null)
    {        
        if ($this->request->is('get')) {

            $order = $this->Orders->get($id, [
                'contain' => [
                    'Customers' => ['Users'],
                    'CustomerAddresses' => ['Cities', 'Municipalities'],
                    'Offers',
                    'Showrooms' => ['Cities'],
                    'OrderItems' => [
                        'Products' => [
                            'ProductImages' => function ($q) {
                                return $q->where(['image_default' => 1, 'status' => 'A']);
                            }
                        ],
                        'ProductVariants' => [
                            'ProductVariantImages' => function ($q) {
                                return $q->where(['image_default' => 1, 'status' => 'A']);
                            }
                        ]
                    ],
                    'Transactions'
                ]
            ]);

            $customer_care_no = $this->SiteSettings->find()
                ->select(['customer_support_no'])
                ->first()->customer_support_no;
            $customer_care_email = $this->SiteSettings->find()
                ->select(['support_email'])
                ->first()->support_email;
            $call_center_no = $this->SiteSettings->find()
                ->select(['contact_no'])
                ->first()->contact_no;
            $whatsapp_no = $this->SiteSettings->find()
                ->select(['contact_no'])
                ->first()->contact_no;
            $after_sales_no = $this->SiteSettings->find()
                ->select(['contact_no'])
                ->first()->contact_no;

            // Assign the CloudFront URL to product images
            foreach ($order->order_items as $item) {
                if (!empty($item->product->product_images)) {
                    foreach ($item->product->product_images as $image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }
                if (!empty($item->product_variant->product_variant_images)) {
                    foreach ($item->product_variant->product_variant_images as $image) {
                        $image->image = $this->Media->getCloudFrontURL($image->image);
                    }
                }
            }

            $orderStatusMap = Configure::read('Constants.ORDER_STATUSES_MAP');
            $orderStatusProgress  = Configure::read('Constants.ORDER_STATUS_PROGRESS_COLOR');
            $orderStatusProgressBar  = Configure::read('Constants.ORDER_STATUS_PROGRESS_BAR');

            $paymentStatusProgress  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_COLOR');
            $paymentStatusProgressBar  = Configure::read('Constants.PAYMENT_STATUS_PROGRESS_BAR');

            $shippedStatusProgress  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_COLOR');
            $shippedStatusProgressBar  = Configure::read('Constants.SHIPPED_STATUS_PROGRESS_BAR');

            $deliveryStatusProgress  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_COLOR');
            $deliveryStatusProgressBar  = Configure::read('Constants.DELIVERY_STATUS_PROGRESS_BAR');

            $orderStatus = Configure::read('Constants.ORDER_STATUSES');
            $currencyConfig = Configure::read('Settings.Currency.format');
            $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
            $orderItemCount = $this->Orders->OrderItems->find()
                ->where(['order_id' => $id])
                ->count();

            
            $resData = ['order'=>$order, 'currencySymbol'=>$currencySymbol, 'orderItemCount'=>$orderItemCount, 'orderStatusMap'=>$orderStatusMap, 'orderStatus'=>$orderStatus, 'orderStatusProgress'=>$orderStatusProgress, 'orderStatusProgressBar'=>$orderStatusProgressBar, 'paymentStatusProgressBar'=>$paymentStatusProgressBar, 'paymentStatusProgress'=>$paymentStatusProgress, 'shippedStatusProgress'=>$shippedStatusProgress, 'shippedStatusProgressBar'=>$shippedStatusProgressBar, 'deliveryStatusProgress'=>$deliveryStatusProgress, 'deliveryStatusProgressBar'=>$deliveryStatusProgressBar, 'customer_care_no'=>$customer_care_no, 'customer_care_email'=>$customer_care_email, 'call_center_no'=>$call_center_no, 'whatsapp_no'=>$whatsapp_no, 'after_sales_no'=>$after_sales_no];

            $result = ['status' => __('success'), 'data' => $resData];
            $this->response = $this->response->withStatus(200);

            /*$this->set(compact(
                'order', 'currencySymbol', 'orderItemCount', 'orderStatusMap', 'orderStatus',
                'orderStatusProgress', 'orderStatusProgressBar', 'paymentStatusProgressBar',
                'paymentStatusProgress', 'shippedStatusProgress', 'shippedStatusProgressBar',
                'deliveryStatusProgress', 'deliveryStatusProgressBar', 'customer_care_no',
                'customer_care_email', 'call_center_no', 'whatsapp_no', 'after_sales_no'
            ));

            // Render the invoice HTML as string
            ob_start();
            $this->render('/Orders/invoice');
            $htmlContent = ob_get_clean();*/
            
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

       /* try {
            // Initialize MPDF with portrait mode (default)
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4', // A4 portrait
                'orientation' => 'P' // "P" for Portrait, "L" for Landscape
            ]);

            // Read all CSS files from webroot/css directory
            $cssDirectory = WWW_ROOT . 'css' . DS;
            $cssFiles = glob($cssDirectory . '*.css'); // Get all CSS files
            $allCss = '';

            foreach ($cssFiles as $file) {
                $allCss .= file_get_contents($file) . "\n"; // Append CSS content
            }

            // Apply styles
            $mpdf->WriteHTML($allCss, \Mpdf\HTMLParserMode::HEADER_CSS);
            $mpdf->WriteHTML($htmlContent, \Mpdf\HTMLParserMode::HTML_BODY);

            // Output PDF
            $pdfFileName = "Invoice_" . time() . ".pdf";
            //$mpdf->Output($pdfFileName, "I"); // Open in browser
            return $this->response->withType('application/pdf')
                ->withHeader('Content-Disposition', 'attachment; filename="' . $pdfFileName . '"')
                ->withStringBody($mpdf->Output('', 'S')); // Return as string

        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]));
        } */       

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function settings()
    {
        // Get the user info from the logged-in user's identity
        $identity = $this->request->getAttribute('identity');
        if($identity) {
             $roleId  = $identity->get('role_id');
             $userId = $identity->get('id');

            // Load the role name based on the role_id
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;
        }  
        $data = $this->request->getQuery();
        //update fcm_token
        if ($data['fcm_token']) {            
            $fcm_token = trim($data['fcm_token']);
            $tokenUpdate = $this->Users->get($userId);           
            $tokenUpdate->fcm_token = $fcm_token;
            $this->Users->save($tokenUpdate);
        }

        $city_abidjan = Configure::read('Constants.ABIDJAN_CITY_ID');
        $orderStatuses = Configure::read('Constants.ORDER_STATUSES');
        $orderstatuses = array_keys($orderStatuses);
        $paymentStatuses = Configure::read('Constants.PAYMENT_STATUSES');
        $paymentstatuses = array_keys($paymentStatuses);
        $dateFormat = Configure::read('Settings.DATE_FORMAT');
        $timeFormat = Configure::read('Settings.TIME_FORMAT');
        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $googlemapkey = Configure::read('Settings.GOOGLE_MAP_API_KEY');
        $cloudfront_url = Configure::read('Settings.CLOUDFRONT_URL');

        $site_settings = $this->SiteSettings->getDetails();
        $transaction_fee_percent = $site_settings['transaction_fee'];

        $data = [
            'currency' => $currencySymbol,
            'google_map_api_key' => $googlemapkey,
            'orderstatuses' => $orderstatuses,
            'paymentstatuses' => $paymentstatuses,
            'ABIDJAN_CITY_ID' => $city_abidjan, 
            'cloudfront_url' => $cloudfront_url,
            'transaction_fee_percent' => $transaction_fee_percent
            /*'dateFormat' => $dateFormat,*/
            /*'timeFormat' =>  $timeFormat */           
        ];

        $result = [
            'status' => 'success',
            'code' => 200,
            'data' => $data,
            'message' => __('Setting listing successfully.')
        ];
        $this->response = $this->response->withStatus(200);

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S get all cities detail
    public function listCity()
    {

        if ($this->request->is('get')) {

            $cities = $this->Cities->listCity();
            if ($cities) {
                $result = ['status' => __('success'), 'data' => $cities];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => __('success'), 'data' => $cities, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S list Municipality
    public function listMunicipality()
    {
        if ($this->request->is('get')) {                         

        $municipalities = $this->Municipalities->listMunicipality();

        $result = [
            'status' => __('success'),
            'code' => 200,
            'data' => $municipalities,
            'message' => __('Municipalities listing successfully')
        ];
        $this->response = $this->response->withStatus(200);
            
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S get all showrooms
    public function listShowroom()
    {

        if ($this->request->is('get')) {

            $showrooms = $this->Showrooms->listShowroom();
            if ($showrooms) {
                $result = ['status' => __('success'), 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => __('success'), 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function login() {

        if ($this->request->is('post')) {
    
            $data = $this->request->getData();
           /* $supervisor_role = $this->Roles->find()
                    ->where(['name' => 'Showroom Supervisor', 'status' => 'A'])
                    ->first();
            $supervisor_role_id = $supervisor_role->id;

            $showroom_manager_role = $this->Roles->find()
                    ->where(['name' => 'Showroom Manager', 'status' => 'A'])
                    ->first();
            $showroom_manager_role_id = $showroom_manager_role->id;

            $sales_person_role = $this->Roles->find()
                    ->where(['name' => 'Sales Person', 'status' => 'A'])
                    ->first();
            $sales_person_role_id = $sales_person_role->id;

            $login_roles = [$supervisor_role_id, $showroom_manager_role_id, $sales_person_role_id];

            $role_name_arr = [$supervisor_role_id=>$supervisor_role->name, $showroom_manager_role_id=>$showroom_manager_role->name, $sales_person_role_id=>$sales_person_role->name];*/

            $roles = $this->Roles->find()
                ->where([
                    'name IN' => ['Showroom Supervisor', 'Showroom Manager', 'Sales Person', 'Admin', 'Warehouse Manager', 'Warehouse Assistant'],
                    'status' => 'A'
                ])
                ->all()
                ->toArray();

            $login_roles = array_column($roles, 'id'); // Extracts role IDs
            $role_name_arr = array_combine($login_roles, array_column($roles, 'name')); // Maps ID to Name
            
            //echo "<pre>"; print_r($login_roles);
            //echo "<pre>"; print_r($role_name_arr);

            /*$request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'supervisor login',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];
    
            $this->ApiRequestLogs->add_new_log($request_attr);*/
    
            if ( isset($data['email']) && $data['email'] && isset($data['password']) && $data['password'] ) {
                
                $userQuery = $this->Users->find();     
                $user = $userQuery->where(['email' => $data['email'], 'role_id IN'=>$login_roles, 'user_type'=>'Admin', 'status' => 'A'])->first();
                if($user){
                    if ($user && password_verify($data['password'], $user->password)) {
                        if ($user['status'] == 'A') {                        

                            $warehouse['id'] = null;
                            $warehouse['name'] = null;

                            $role_name = $role_name_arr[$user['role_id']];
                            if($role_name == 'Showroom Supervisor'){
                                $cond = ['Showrooms.showroom_supervisor'=>$user['id']];
                                $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                                if(!$showroom){
                                    $result = ['status' => 'error', 'message' => __('Showroom is not assigned to this user yet')];
                                    goto label;
                                }

                            } else if($role_name == 'Showroom Manager') {
                                $cond = ['Showrooms.showroom_manager'=>$user['id']];
                                $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                                if(!$showroom){
                                    $result = ['status' => 'error', 'message' => __('Showroom is not assigned to this user yet')];
                                    goto label;
                                }

                            } else if($role_name == 'Sales Person'){
                                $showroom = $this->Showrooms->find()
                                    ->select(['Showrooms.id', 'Showrooms.name'])
                                    ->innerJoinWith('ShowroomUsers')
                                    ->where(['ShowroomUsers.user_id' => $user['id']])
                                    ->order(['Showrooms.name' => 'ASC'])
                                    ->limit(1)
                                    ->first(); // Fetches only the first result
                                if(!$showroom){
                                    $result = ['status' => 'error', 'message' => __('Showroom is not assigned to this user yet')];
                                    goto label;
                                }

                            } else {
                                //$showroom['id'] = null;
                                //$showroom['name'] = null;
                                $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A'])->order(['name'=>'ASC'])->limit(1)->first();

                                 if($role_name == 'Warehouse Manager'){
                                    $cond = ['Warehouses.manager_id'=>$user['id']];
                                    $warehouse = $this->Warehouses->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();

                                 } else if ($role_name == 'Warehouse Assistant') {
                                    $cond = ['Warehouses.assistant_id'=>$user['id']];
                                    $warehouse = $this->Warehouses->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                                 }
                            }                         
                            
                            $user->last_login = FrozenTime::now();
                            $user->token = Security::hash(Security::randomBytes(32));
                            $this->Users->save($user);

                            if($user['profile_pic']) {
                               $user['profile_pic'] = $this->Media->getCloudFrontURL($user['profile_pic']);
                            }

                            $user_data = array(
                                'user_id' => $user['id'],
                                'role_id' => $user['role_id'],
                                'token' => $user['token'],
                                'role_name' => $role_name,   
                                'showroom_id' => $showroom['id'],
                                'showroom_name' => $showroom['name'], 
                                'warehouse_id' => $warehouse['id'],
                                'warehouse_name' => $warehouse['name'],                 
                                'first_name' => $user['first_name'],
                                'last_name' => $user['last_name'],
                                'email' => $user['email'],
                                'country_code' => $user['country_code'],
                                'mobile_no' => $user['mobile_no'],
                                'profile_pic' => $user['profile_pic'],
                                'last_login' => $user['last_login']
                            );

                            /*modules and permissions*/
                            $permissionsArray = [];
                            $accessibleModules = [];                                

                            $modules = $this->Modules->find('all')->toArray();

                            foreach ($modules as $module) {
                                $permissionsArray[$module->name] = $this->getModulePermissions($user['role_id'], $module->id);
                            }

                            foreach ($permissionsArray as $moduleName => $permissions) {
                                if ($permissions['can_view'] == 1) {
                                    $accessibleModules[] = $moduleName;
                                }
                            }

                            /*$canView = $this->hasPermission($this->request->getParam('controller'), 'index');
                            $canAdd  = $this->hasPermission($this->request->getParam('controller'), 'add');
                            $canEdit = $this->hasPermission($this->request->getParam('controller'), 'edit');
                            $canDelete = $this->hasPermission($this->request->getParam('controller'), 'delete');
                            $canApprove = $this->hasPermission($this->request->getParam('controller'), 'approve');
                            $storeBased = $this->hasPermission($this->request->getParam('controller'), 'store');
                            $this->checkPermission();*/
                           
                            //echo "<pre>"; print_r($permissionsArray); die;
                            
                            //$result = ['status' => 'success', 'data' => $user_data, 'message' => __('You are logged in successfully!')];
                            $result = ['status' => 'success', 'data' => ['user_data' => $user_data, 'accessibleModules' => $accessibleModules, 'permissionsArray' => $permissionsArray], 'message' => __('You are logged in successfully!')];
                            $this->response = $this->response->withStatus(200);  
                        } else {
                            //$this->Authentication->logout();
                            $result = ['status' => 'error', 'message' => __('Your account is inactive or deleted')];
                            $this->response = $this->response->withStatus(200);
                        }
                    } else {
                        $result = ['status' => 'error', 'message' => __('Email or Password is incorrect')];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = ['status' => 'error', 'message' => __('Incorrect Email Or You are not authorized to login to this App')];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = ['status' => 'error', 'message' => __('Invalid parameters')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    } 

    //S
    public function forgotPassword() {

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $email = $data['email'];
            
            if (isset($data['email']) && $data['email']) {
                $user = $this->Users->find()->where(['email' => $data['email'], 'status' => 'A', 'user_type' => 'Admin'])->first();
                if (isset($user->id) && $user->id) {
                    
                    $user_id = $user->id;
                    $date_time = date('d-m-Y H:i:s');
                    
                    $token = Security::hash(Security::randomBytes(25));
                    $user->password_reset_token = $token;
                    $user->token_created_at = date('Y-m-d H:i:s');
                    $saved = $this->Users->save($user);

                    if($saved){

                        $web_url = Configure::read('Settings.SITE_URL');
                        $resetLink = $web_url.'Users/resetPassword/'.$token;

                        $firstname = $user->first_name;
                        $lastname  = $user->last_name;
                        $to        = $user->email;
                        $from      = Configure::read('Settings.ADMIN_EMAIL');
                        $subject   = "Reset Password";
                        $template  = "supervisor_forgot_password";                     
                        $viewVars = array('resetLink'=>$resetLink, 'token' => $token, 'userId' => $user->id, 'username' => $user->first_name . ' ' . $user->last_name, 'datetime' => date('d-m-Y H:i:s'));

                        $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                        //$sendEmail = 1;
                        if ($sendEmail) {
                            $result = ['status' => 'success', 'message' => __('If there is an account associated with '.$email.', you will receive an email to reset password')];
                        } else {
                            $result = ['status' => 'error', 'message' => __('Something went wrong ! please try after some time')];
                        }
                    } else {
                        $result['status'] = 'error';
                        $result['message'] = __('Unable to send the password reset email. Please try again.');
                    }

                } else {
                    $result['status'] = 'error';
                    $result['message'] = __('Please enter a registered email');
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = __('Invalid parameters');
            }
        } else {
            $result['status'] = 'error';
            $result['message'] = 'Method not allowed';
        }
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function resetPassword() {

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            if (isset($data['password']) && $data['password'] && isset($data['link']) && $data['link']) {
                $url_array = explode("/", $data['link']);
                $date_time = base64_decode(end($url_array));
                array_pop($url_array);
                $user_id = base64_decode(end($url_array));
                $time_diff = abs(strtotime($date_time) - strtotime(date('d-m-Y H:i:s')));
                $minutes = round($time_diff / 60);
                if ($minutes > 720) {  // expires after 12 hours
                    $result['status'] = 'error';
                    $result['message'] = 'Your password reset link has expired';
                } else {
                    $user = $this->Users->get($user_id);
                    $user = $this->Users->patchEntity($user, ['password' => trim($data['password'])]);
                    if ($this->Users->save($user)) {
                        $result['status'] = 'success';
                        $result['message'] = 'Password reset successfully';
                    } else {
                        $result['status'] = 'error';
                        $result['message'] = 'Something went wrong ! please try after some time';
                    }
                }
            } else {
                $result['status'] = 'error';
                $result['message'] = 'Invalid parameters given';
            }
        } else {
            $result['status'] = 'error';
            $result['message'] = 'Method not allowed';
        }
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    // S get all showrooms
    public function listShowroomByUser () {

       if ($this->request->is('get')) {            
            /*if($role_name == 'Showroom Supervisor'){
                $showrooms = $this->Zones->listShowroomByUser($user_id);
            } else if($role_name == 'Showroom Manager') {
                $showrooms = $this->Showrooms->listShowroomByUser($user_id);
            } else {
                $showrooms = 0;
            } */          
            
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }           

            $showrooms = $this->Showrooms->listShowroomByUser($userId, $roleName);
            
            if ($showrooms) {
                $result = ['status' => __('success'), 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function home() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $identity->get('_matchingData')['Roles']['name'];
            }             

            $data = $this->request->getData();
            $showroomId = $data['showroom_id'];

            if (!$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id is required')
                ];
                goto label;
            }

            $overview = array();
            //total sales
            $sales = $this->Orders->getCurrentDaySales($showroomId);
            $overview['total_sales'] = $sales;

            //total expenses
            $expenses = $this->ShowroomExpenses->getCurrentDayExpenses($showroomId);
            $overview['total_expenses'] = $expenses;

            //total_orders
            $orders = $this->Orders->getCurrentDayOrders($showroomId);
            $overview['total_orders'] = $orders;

            //recent activities (Recent Activities shows 1. Latest Order 2. Latest Expense Payments 3. Order Return or Refund  4. Stock Recieved)
            $recentOrders = $this->Orders->find()
            ->select(['Orders.id','Orders.order_number','Orders.total_amount','Orders.order_date','Orders.status', 'Orders.created'])
            ->where(['Orders.showroom_id' => $showroomId, 'Orders.status' => 'Pending'])
            ->order(['Orders.created' => 'DESC'])
            ->limit(5)
            ->all();

            $recentExpenses = $this->ShowroomExpenses->find()
            ->select(['ShowroomExpenses.id','ShowroomExpenses.expense_category_id','ShowroomExpenses.amount','ShowroomExpenses.incurred_date','ShowroomExpenses.payment_status','ShowroomExpenses.created'])
            ->where(['showroom_id' => $showroomId, 'status' => 'A'])
            ->order(['ShowroomExpenses.created' => 'DESC'])
            ->limit(5)
            ->all();

           $returnOrders = $this->OrderReturns->find()
            ->select(['id', 'status', 'reason','requested_at', 'created']) 
            ->contain([ 
                'OrderReturnCategories' => function ($q3) {
                    return $q3
                    ->select(['OrderReturnCategories.name']);
                    },           
                'Orders' => function ($q) use ($showroomId) {
                    return $q
                        ->select(['Orders.id', 'Orders.order_number'])
                        ->where(['Orders.showroom_id' => $showroomId]);
                    }
                ])
            ->order(['OrderReturns.created' => 'DESC'])
            ->limit(5)
            ->all();

            $stockRecieved = $this->StockMovements->find()
            ->select(['StockMovements.id','StockMovements.movement_date','StockMovements.referenceID', 'StockMovements.verify_status','Showrooms.id','Showrooms.name', 'StockMovements.created'])
            ->contain(['Showrooms'])
            ->where(['showroom_id' => $showroomId, 'movement_type' => 'Incoming', 'reference_type' => 'stock_request'])
            ->order(['StockMovements.created' => 'DESC'])
            ->limit(5)
            ->all();

            // Merge all data into a single array with a common timestamp key
            $activities = [];
            foreach ($recentOrders as $order) {
                $activities[] = ['type' => 'order', 'data' => $order, 'timestamp' => $order->created];
            }
            foreach ($recentExpenses as $expense) {
                $activities[] = ['type' => 'expense', 'data' => $expense, 'timestamp' => $expense->created];
            }
            foreach ($returnOrders as $orderReturn) {
                $activities[] = ['type' => 'order_return', 'data' => $orderReturn, 'timestamp' => $orderReturn->created];
            }
            foreach ($stockRecieved as $stock) {
                $activities[] = ['type' => 'stock_received', 'data' => $stock, 'timestamp' => $stock->created];
            }

            // Sort all activities by timestamp in descending order
            usort($activities, function ($a, $b) {
                return $b['timestamp'] <=> $a['timestamp'];
            });

            // Return the top $limit activities after sorting
            // array_slice($activities, 0, $limit);

            //revenue-expense graph (Day)
            $financialData = $this->revenueExpenseForDay($showroomId);

            //total/average expenses
            $expensesSummary = $this->getExpensesSummary('day', $showroomId);

            //recent expenses
            $expenseLimit = 5;
            $recentExpenses = $this->ShowroomExpenses->getRecentExpenses($showroomId, $expenseLimit);
            
            //pending approvals
            $pendingActions = $this->UserActions->getPendingActions($userId);


            $result = ['status' => __('success'), 'data' => ['overview'=>$overview, 'recentActivities'=>$activities, 'financialData'=>$financialData, 'expensesSummary'=>$expensesSummary, 'recentExpenses'=>$recentExpenses, 'pendingActions'=>$pendingActions]];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function homeOverviewFilter() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getData();
            $showroomId = $data['showroom_id'];
            $period = $data['period'];

            if (!$period || !$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period are required')
                ];
                goto label;
            }

            $overview = array();

            if($period == 'Day') {

                $sales   = $this->Orders->getCurrentDaySales($showroomId); 
                $expenses   = $this->ShowroomExpenses->getCurrentDayExpenses($showroomId);
                $orders   = $this->Orders->getCurrentDayOrders($showroomId);
            } else if($period == 'Month') {

                $sales = $this->Orders->getCurrentMonthSales($showroomId);
                $expenses = $this->ShowroomExpenses->getCurrentMonthExpenses($showroomId);
                $orders = $this->Orders->getCurrentMonthOrders($showroomId);
            } else if ($period == 'Year') {

                $sales  = $this->Orders->getCurrentYearSales($showroomId);
                $expenses  = $this->ShowroomExpenses->getCurrentYearExpenses($showroomId);
                $orders  = $this->Orders->getCurrentYearOrders($showroomId);
            } else { }

            $overview['total_sales'] = $sales;
            $overview['total_expenses'] = $expenses;
            $overview['total_orders'] = $orders;

            $result = ['status' => __('success'), 'data' => $overview];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function homeGraphFilter() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'];
            $period = $data['period'];

            if (!$period || !$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period are required')
                ];
                goto label;
            }

            if($period == 'Day') {
                $financialData = $this->revenueExpenseForDay($showroomId);
                //total/average expenses
                $expensesSummary = $this->getExpensesSummary('day', $showroomId);

            } else if($period == 'Month') {
                $financialData = $this->revenueExpenseForMonth($showroomId);
                //total/average expenses
                $expensesSummary = $this->getExpensesSummary('month', $showroomId);

            } else if ($period == 'Year') {
                $financialData = $this->revenueExpenseForYear($showroomId);
                //total/average expenses
                $expensesSummary = $this->getExpensesSummary('year', $showroomId);

            } else { }

            $result = ['status' => __('success'), 'data' => ['financialData'=>$financialData, 'expensesSummary'=>$expensesSummary] ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function salesDashboard(){

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }           

            $data = $this->request->getData();
            $showroomId = $data['showroom_id'];
            $period = $data['period']; // Expecting 'Day', 'Month', or 'Year'

            if (!$showroomId || !in_array($period, ['Day', 'Month', 'Year'])) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period (Day, Month, or Year) are required')
                ];
                goto label;
            }
            
            //showroom detail
            $showroomDetail = $this->Showrooms->showroomDetailById($showroomId);
            if ($showroomDetail['image']) {
                $showroomDetail['image'] = $this->Media->getCloudFrontURL($showroomDetail['image']);
            }

            // Get sales data based on the period (default Day)            
            $salesData = $this->salesDataForDay($showroomId);            
            
            //get count of orders by status
            $order_statuses = ['Pending', 'Processing', 'Delivered'];
            foreach ($order_statuses as $status) {
                $query = $this->Orders->find()
                    ->where(['Orders.status' => $status, 'Orders.showroom_id' => $showroomId]);

                // Count the number of orders for the current status
                $orderStatusCount[$status] = $query->count();
            }

            //stock levels
            //$lowStock = $this->ProductStocks->showroomLowStock($showroomId);
            //$highStock = $this->ProductStocks->showroomHighStock($showroomId);

            $lowStock = 0; $highStock = 0; $min_product_quantity =  $showroomDetail['min_product_quantity'];
            $stocks = $this->ProductStocks->showroomStockLevel($showroomId);
            if($stocks) {
                foreach ($stocks as $stock_k => $stock_v) {
                     // Query to fetch min_product_quantity for the current product
                    /*$categoryData = $this->Products->find()
                        ->select(['Categories.min_product_quantity'])
                        ->leftJoinWith('ProductCategories.Categories')
                        ->where(['Products.id' => $stock_v['product_id']])
                        ->first();*/

                    // Assign min_product_quantity if available
                    //$min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'];

                    if($stock_v['quantity'] < $min_product_quantity)
                    {
                        $lowStock = $lowStock+1; 
                    } else {
                        $highStock = $highStock+1;
                    }
                } 
            }           
           
            $stockLevels = ['lowStock'=>$lowStock, 'highStock' =>$highStock];

            //recent orders (Pending)
            //$orderLimit = 10;
            $recentOrders = $this->Orders->getRecentOrders($showroomId);

            //recent order payments
            $paymentLimit = 5;
            $recentPayments = $this->Transactions->getRecentPayments($showroomId, $paymentLimit);

            //pending actions
            $pendingActions = $this->UserActions->getPendingActions1($userId);
             // Include dynamic referenceID data
            foreach ($pendingActions as $pAction) {
                if ($pAction['referenceTable'] === 'orders') {
                    $referenceData = $this->Orders->find()
                        ->select(['id', 'total_amount'])
                        ->where(['id' => $pAction['referenceID']])
                        ->first();
                    $pAction['reference_data'] = $referenceData ? $referenceData->toArray() : null;
                } elseif ($pAction['referenceTable'] === 'showroom_expenses') {
                    $referenceData = $this->ShowroomExpenses->find()
                        ->select(['id', 'amount'])
                        ->where(['id' => $pAction['referenceID']])
                        ->first();
                    $pAction['reference_data'] = $referenceData ? $referenceData->toArray() : null;
                } else {
                    $pAction['reference_data'] = null;
                }
            }

            //order return
            $orderReturnLimit = 5;
            $OrderReturns = $this->OrderReturns->getOrderReturns($showroomId, $orderReturnLimit);

            $result = ['status' => __('success'), 'data' => [
                'showroomDetail'=>$showroomDetail,
                'salesData'=>$salesData,
                'orderStatusCount'=>$orderStatusCount,
                'stockLevels' =>$stockLevels,
                'recentOrders'=>$recentOrders,
                'recentPayments'=>$recentPayments,
                'pendingActions'=>$pendingActions,
                'OrderReturns'=>$OrderReturns
            ]];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function salesGraphFilter() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             

            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'];
            $period = $data['period'];

            if (!$period || !$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period are required')
                ];
                goto label;
            }

            // Get sales data based on the period
            if($period == 'Day') {
                $salesData = $this->salesDataForDay($showroomId);
            } else if ($period == 'Month') {
                $salesData = $this->salesDataForMonth($showroomId);
            } else if ($period == 'Year'){
                $salesData = $this->salesDataForYear($showroomId);
            } else {
                $salesData = $this->salesDataForDay($showroomId);
            }

            $result = ['status' => __('success'), 'data' => ['salesData'=>$salesData] ];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S Create a function to generate dates for the last 7 days, days of the current month, or months of the last 6 months.
    public function generateDateRange($timeframe)
    {
        $dates = [];
        if ($timeframe === 'Day') {
            // Last 7 days
            for ($i = 6; $i >= 0; $i--) {
                $dates[] = FrozenDate::now()->subDays($i)->format('Y-m-d');
            }
        } elseif ($timeframe === 'Month') {
            // Days in the current month
            $currentDate = FrozenDate::now();
            $firstDay = $currentDate->firstOfMonth();
            $lastDay = $currentDate->lastOfMonth();
            while ($firstDay <= $lastDay) {
                $dates[] = $firstDay->format('Y-m-d');
                $firstDay = $firstDay->addDays(1);
            }
        } elseif ($timeframe === 'Year') {
            // Last 6 months
            for ($i = 5; $i >= 0; $i--) {
                $month = FrozenDate::now()->subMonths($i);
                $dates[] = $month->format('Y-m'); // Month format
            }
        }
        return $dates;
    }

    //S
    public function getExpensesSummary($type, $showroomId)
    {
        $query = $this->ShowroomExpenses->find();

        // Select total and average expenses
        $result = $query->select([
            'total_expenses' => $query->func()->sum('amount'), // Total sum of the expenses
            'average_expenses' => $query->func()->avg('amount'), // Average of the expenses
        ]);

        // Add conditions based on the time type
        if ($type === 'day') {
            // Last 7 days
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');
            $result->where(function ($exp) use ($startDate, $endDate) {
                return $exp->between('incurred_date', $startDate . ' 00:00:00', $endDate . ' 23:59:59');
            });
        } elseif ($type === 'month') {
            // Current month
            $currentMonth = date('Y-m');
            $result->where(function ($exp) use ($currentMonth) {
                return $exp->like('incurred_date', $currentMonth . '%'); // Match 'YYYY-MM' format
            });
        } elseif ($type === 'year') {
            // Last 6 months
            $startDate = date('Y-m-d', strtotime('-6 months'));
            $endDate = date('Y-m-d');
            $result->where(function ($exp) use ($startDate, $endDate) {
                return $exp->between('incurred_date', $startDate . ' 00:00:00', $endDate . ' 23:59:59');
            });
        }       

        $result->where(['showroom_id' => $showroomId]);

        $data = $result->first();

        // Handle case where no expenses exist
        if (!$data || $data->total_expenses === null) {
            return [
                'total_expenses' => 0,
                'average_expenses' => 0,
            ];
        }

        return $data;
    }

    //S
    public function revenueExpenseForDay($showroomId)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subDays(6);          // 7 days ago (including today)        

        // Query to fetch revenue for each of the last 7 days
        $revenueQuery = $this->Orders->find()
            ->select([
                'date' => 'DATE(order_date)',             // Group by date
                'total_revenue' => 'SUM(total_amount)'    // Sum of revenue per day
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status !=' => 'Cancelled',
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE(order_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        // Query to fetch expense for each of the last 7 days
        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'date' => 'DATE(incurred_date)',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE(incurred_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();
            

        // Process the result to ensure each day in the range has a value
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = $endDate->subDays($i)->format('Y-m-d');
            $revenue = 0; $expense = 0;

            // Search for the revenue/expense data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['date'] === $date) {
                    $revenue = $row['total_revenue'];
                    break;
                }
            }
            foreach ($expenseQuery as $row) {
                if ($row['date'] === $date) {
                    $expense = $row['total_expense'];
                    break;
                }
            }

            $data[] = [
                'date' => $date,
                'revenue' => $revenue,
                'expense' => $expense
            ];
        }

        return $data;
    }

    //S
    public function revenueExpenseForMonth($showroomId)
    {
        $endDate = FrozenDate::now();                 // Today's date
        $startDate = $endDate->firstOfMonth();        // First day of the current month

        // Query to fetch revenue for each day of the current month
        $revenueQuery = $this->Orders->find()
            ->select([
                'date' => 'DATE(order_date)',             // Group by date
                'total_revenue' => 'SUM(total_amount)'    // Sum of revenue per day
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status !=' => 'Cancelled',
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE(order_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'date' => 'DATE(incurred_date)',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE(incurred_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        // Process the result to ensure each day in the month has a value
        $data = [];
        $currentDate = $startDate;

        // Loop through all days of the current month
        while ($currentDate <= $endDate) {
            $date = $currentDate->format('Y-m-d');
            $revenue = 0; $expense = 0;

            // Search for the revenue data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['date'] === $date) {
                    $revenue = $row['total_revenue'];
                    break;
                }
            }
            foreach ($expenseQuery as $row) {
                if ($row['date'] === $date) {
                    $expense = $row['total_expense'];
                    break;
                }
            }

            $data[] = [
                'date' => $date,
                'revenue' => $revenue,
                'expense' => $expense
            ];

            // Move to the next day
            $currentDate = $currentDate->addDays(1);
        }

        return $data;
    }

    //S
    public function revenueExpenseForYear($showroomId)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subMonths(5)->firstOfMonth();  // First day of the month, 6 months ago

        // Query to fetch revenue for each month within the last 6 months
        $revenueQuery = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(order_date, "%Y-%m")',  // Group by year and month
                'total_revenue' => 'SUM(total_amount)'         // Sum of revenue per month
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status !=' => 'Cancelled',
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE_FORMAT(order_date, "%Y-%m")')        // Group by month
            ->enableHydration(false)                     // Return as an array, not entity objects
            ->toArray();

        // Query to fetch expense for each month within the last 6 months
        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'month' => 'DATE_FORMAT(incurred_date, "%Y-%m")',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE_FORMAT(incurred_date, "%Y-%m")')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        // Process the result to ensure each month in the range has a value
        $data = [];
        $currentMonth = $startDate;

        // Loop through all months within the last 6 months
        while ($currentMonth <= $endDate) {
            $month = $currentMonth->format('Y-m');
            $revenue = 0; $expense = 0;

            // Search for the revenue data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['month'] === $month) {
                    $revenue = $row['total_revenue'];
                    break;
                }
            }
            foreach ($expenseQuery as $row) {
                if ($row['month'] === $month) {
                    $expense = $row['total_expense'];
                    break;
                }
            }

            $data[] = [
                'month' => $month,
                'revenue' => $revenue,
                'expense' => $expense
            ];

            // Move to the next month
            $currentMonth = $currentMonth->addMonths(1);
        }

        return $data;
    }


    //S
    public function salesDataForDay($showroomId)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subDays(6);          // 7 days ago (including today)

        // Query to fetch revenue for each of the last 7 days
        $revenueQuery = $this->Orders->find()
            ->select([
                'date' => 'DATE(order_date)',             // Group by date
                'total_sale' => 'SUM(total_amount)'    // Sum of revenue per day
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status NOT IN'=>['Cancelled', 'Returned'],
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE(order_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        // Process the result to ensure each day in the range has a value
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = $endDate->subDays($i)->format('Y-m-d');
            $revenue = 0;

            // Search for the revenue/expense data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['date'] === $date) {
                    $revenue = $row['total_sale'];
                    break;
                }
            }

            $data[] = [
                'date' => $date,
                'sale' => $revenue
            ];
        }

        return $data;
    }

    //S
    public function salesDataForMonth($showroomId)
    {
        $endDate = FrozenDate::now();                 // Today's date
        $startDate = $endDate->firstOfMonth();        // First day of the current month

        // Query to fetch revenue for each day of the current month
        $revenueQuery = $this->Orders->find()
            ->select([
                'date' => 'DATE(order_date)',             // Group by date
                'total_sale' => 'SUM(total_amount)'    // Sum of revenue per day
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status NOT IN'=>['Cancelled', 'Returned'],
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE(order_date)')
            //->order(['order_date' => 'ASC'])
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();

        // Process the result to ensure each day in the month has a value
        $data = [];
        $currentDate = $startDate;

        // Loop through all days of the current month
        while ($currentDate <= $endDate) {
            $date = $currentDate->format('Y-m-d');
            $revenue = 0;

            // Search for the revenue data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['date'] === $date) {
                    $revenue = $row['total_sale'];
                    break;
                }
            }
            $data[] = [
                'date' => $date,
                'sale' => $revenue
            ];

            // Move to the next day
            $currentDate = $currentDate->addDays(1);
        }

        return $data;
    }

    //S
    public function salesDataForYear($showroomId)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subMonths(5)->firstOfMonth();  // First day of the month, 6 months ago

        // Query to fetch sales for each month within the last 6 months
        $revenueQuery = $this->Orders->find()
            ->select([
                'month' => 'DATE_FORMAT(order_date, "%Y-%m")',  // Group by year and month
                'total_sale' => 'SUM(total_amount)'         // Sum of revenue per month
            ])
            ->where([
                'DATE(order_date) >=' => $startDate->format('Y-m-d'),
                'DATE(order_date) <=' => $endDate->format('Y-m-d'),
                'Orders.status NOT IN'=>['Cancelled', 'Returned'],
                'Orders.showroom_id' => $showroomId
            ])
            ->group('DATE_FORMAT(order_date, "%Y-%m")')        // Group by month
            //->order(['order_date' => 'ASC'])
            ->enableHydration(false)                     // Return as an array, not entity objects
            ->toArray(); 

        // Process the result to ensure each month in the range has a value
        $data = [];
        $currentMonth = $startDate;

        // Loop through all months within the last 6 months
        while ($currentMonth <= $endDate) {
            $month = $currentMonth->format('Y-m');
            $revenue = 0;

            // Search for the revenue data in the query result
            foreach ($revenueQuery as $row) {
                if ($row['month'] === $month) {
                    $revenue = $row['total_sale'];
                    break;
                }
            }

            $data[] = [
                'month' => $month,
                'sale' => $revenue
            ];

            // Move to the next month
            $currentMonth = $currentMonth->addMonths(1);
        }

        return $data;
    }

    //S list sales person by showroom
    public function listSalesPersonsByShowroom ($showroom_id) {

       if ($this->request->is('get')) {

            $salesPersons = $this->Users->find('salesPersonsByShowroom', [
                'showroom_id' => $showroom_id
            ])->toArray();
            if ($salesPersons) {
                $result = ['status' => __('success'), 'data' => $salesPersons];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $salesPersons, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S list and search showrooms
    public function allShowroom () {

       if ($this->request->is('get')) {            

            $showrooms = $this->Showrooms->showroom();
            if ($showrooms) {
                $result = ['status' => __('success'), 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S list and search showrooms by user
    public function showroomByUser () {

       if ($this->request->is('get')) {            
            /*if($role_name == 'Showroom Supervisor'){
                $showrooms = $this->Zones->showroomByUser($user_id);
            } else if($role_name == 'Showroom Manager') {
                $showrooms = $this->Showrooms->showroomByUser($user_id);
            } else {
                $showrooms = 0;
            }*/ 

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getQuery();
            $search_str = $data['search_str'];

            $showrooms = $this->Showrooms->showroomByUser($userId, $roleName, $search_str);          
            
            if ($showrooms) {
                $result = ['status' => __('success'), 'data' => $showrooms];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $showrooms, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }     

    
    //S list users of role Showroom
    public function listShowroomUser () {

       if ($this->request->is('get')) {            

            $users = $this->Users->fetchShowroomUser();
            if ($users) {
                $result = ['status' => __('success'), 'data' => $users];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S list unassigned users of role Showroom
    public function unassignedShowroomUser () {

       if ($this->request->is('get')) {            

            $users = $this->Users->findUnassignedSalesPersons();
            if ($users) {
                $result = ['status' => __('success'), 'data' => $users];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S view assigned user
    public function viewShowroomUser ($showroom_id) {

       if ($this->request->is('get')) {            

            $users = $this->ShowroomUsers->viewUser($showroom_id);
            if ($users) {
                $result = ['status' => 'success', 'data' => $users];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'success', 'data' => [], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }
    
    //S assign user
    public function assignUserToShowroom () {

       if ($this->request->is('post')) {            

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData();
            $data['created_by'] = $userId;

            $exists = $this->ShowroomUsers->exists(['user_id' => $data['showroom_user_id'], 'status' => 'A']); 
            if($exists){
                $result = ['status' => __('error'), 'message' => _('User is already assigned to showroom.')];
                goto label;
            }

            $add = $this->ShowroomUsers->addUser($data);
            if ($add) {
                $result = ['status' => __('success'), 'message' => _('Added successfully')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('error'), 'message' => __('Not added')];
                //$this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S unassign user
    public function removeUserFromShowroom () {

       if ($this->request->is('post')) {            

            $data = $this->request->getData();
            $id = $data['id'];
            $remove = $this->ShowroomUsers->removeUser($id);
            if ($remove) {
                $result = ['status' => __('success'), 'message' => __('Deleted successfully')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('error'), 'message' => __('Deletion failed')];
                //$this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S logout
    public function logout() {

        if ($this->request->is('get')) {  

            // Get the token from headers
            //$authHeader = $this->request->getHeaderLine('Authorization');
            /*if (empty($authHeader)) {
                return $this->response->withStatus(400)->withStringBody('Token is required');
            }*/

            // Extract the token (e.g., "Bearer <token>")
            //$token = str_replace('Bearer ', '', $authHeader);          

            $identity = $this->request->getAttribute('identity');
            if ($identity) {                
                $userId  = $identity->get('id');
                //$this->Authentication->logout();

                // Find the user by id/token
                $user = $this->Users->find()
                    ->where(['id' => $userId])
                    ->first();

                if (!$user) {
                    $result = ['status' => __('error'), 'message' => __('Invalid or expired token')];
                    $this->response = $this->response->withStatus(401);
                }

                // Nullify the token and optionally update last_logged_out_at
                $user->token = null;

                if ($this->Users->save($user)) {
                    $result = ['status' => __('success'), 'message' => __('You are successfully logged out')];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => __('error'), 'message' => __('Unable to log out')];
                    $this->response = $this->response->withStatus(500);
                }
                
            } else {
                $result = ['status' => __('error'), 'message' => __('Invalid or expired token')];
                $this->response = $this->response->withStatus(401);
            }                     
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S list and search/filter order    
    public function listOrders() {

       if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');                 

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                $data = $this->request->getQuery();

                if($data['showroom_id']) {
                    $showroom = $data['showroom_id'];
                } else {
                    if($roleName == 'Showroom Supervisor'){
                        $cond = ['Showrooms.showroom_supervisor'=>$userId];
                        $showroom = $this->Showrooms->find()
                        ->select(['id'])
                        ->where([$cond])
                        ->all() // Fetch results
                        ->extract('id') // Extract IDs
                        ->toList(); // Convert to an array
                    } else if($roleName == 'Showroom Manager') {
                        $cond = ['Showrooms.showroom_manager'=>$userId];
                        $showroom = $this->Showrooms->find()
                        ->select(['id'])
                        ->where([$cond])
                        ->all() // Fetch results
                        ->extract('id') // Extract IDs
                        ->toList(); // Convert to an array
                    } else if($roleName == 'Sales Person'){
                        $showroom = $this->Showrooms->find()
                            ->select(['Showrooms.id'])
                            ->innerJoinWith('ShowroomUsers')
                            ->where(['ShowroomUsers.user_id' => $userId])
                            ->all()->extract('id')->toList(); // Convert to an array
                    } 
                }
            } 
            
            if($data['page']){
                $page = $data['page'];
            }else{
                $page = 1;
            }
            if($data['limit']){
                $limit = $data['limit'];
            }else{
                $limit = 10;
            }

            $filter_order_status = $data['order_status'];
            $filter_payment_status = $data['payment_status'];
            $filter_sdate = $data['sdate'];
            //$filter_edate = $data['edate'];
            //$filter_month = $data['month'];

             // Change this to 'month', 'quarter', or 'year' based on your requirement.
            if($data['time_period']){
                $time_period = $data['time_period'];
            } else {
                $time_period = '';
                $startDate  = '';
                $endDate = '';
            }
            if( $time_period) {
                switch ($time_period) {
                    case 'month':
                        // Start and end of the current month
                        $startDate = (new \DateTime('first day of this month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of this month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'quarter':
                        // Determine the current quarter
                        $quarter = ceil(date('n') / 3);
                        $firstMonthOfQuarter = ($quarter - 1) * 3 + 1;
                        $lastMonthOfQuarter = $quarter * 3;

                        // Start and end of the current quarter
                        $startDate = (new \DateTime('first day of ' . $firstMonthOfQuarter . ' month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of ' . $lastMonthOfQuarter . ' month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'year':
                        // Start and end of the current year
                        $startDate = (new \DateTime('first day of January'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of December'))->format('Y-m-d 23:59:59');
                        break;

                    default:
                        $this->response = $this->response->withStatus(400);
                        $result = [
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('Invalid time period')
                        ];
                        goto label;
                }
            }

            $search_str = $data['search_str'];

            $orders = $this->Orders->listOrderByShowroom($showroom, $filter_order_status, $filter_payment_status, $filter_sdate, $time_period, $startDate, $endDate, $search_str, $page, $limit);         
            
            if ($orders) {
                $result = ['status' => __('success'), 'data' => $orders];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $orders, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S order detail
    public function orderDetail_OLD($orderId) {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getQuery();

            $order = $this->Orders->orderDetail($orderId); 
            $total_quantity = $this->OrderItems->find()
                ->select(['total_qty' => 'SUM(OrderItems.quantity)'])
                ->where(['OrderItems.order_id' => $orderId])->first();
            $order['total_quantity'] = $total_quantity->total_qty;       
            
            if ($order) {
                $result = ['status' => __('success'), 'data' => $order];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $order, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
        
    } 

    //S view order detail
    public function orderDetail($orderId)
    {
        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(401);
            } else {

                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;   

                if($roleName == 'Showroom Manager') {
                    $cond = ['Showrooms.showroom_manager'=>$userId];
                    $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                    $currentShowroomId = $showroom['id'];
                }             

                // Fetch the order for the given order ID
                $order = $this->Orders->orderDetail($orderId);

                if (!$order) {
                    $result = [
                        'status' => 'error',
                        'message' => __('Order not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }

                // Fetch site settings for cancellation and return periods
                $siteSettings = $this->SiteSettings->find()
                    ->select(['product_cancel_in_days', 'product_return_in_days'])
                    ->first();

                if($order->cheque_photo){
                    $order->cheque_photo = $this->Media->getCloudFrontURL($order->cheque_photo); 
                }
                // Prepare order data
                $orderData = [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'order_date' => $order->order_date,
                    'total_amount' => $order->total_amount,
                    'subtotal_amount' => $order->subtotal_amount,
                    'delivery_charge' => $order->delivery_charge,
                    'discount_amount' => $order->discount_amount,
                    'delivery_mode' => $order->delivery_mode,
                    'delivery_mode_type' => $order->delivery_mode_type,
                    'order_type' => $order->order_type,
                    'status' => $order->status,
                    'status_date' => $order->status_date,
                    'shipment_status' => $order->shipment_status,
                    'payment_method' => $order->payment_method,
                    'cheque_photo' => $order->cheque_photo,
                    'shipping_method' => $order->shipping_method,
                    'delivery_address' => $order->customer_address ? [                        
                        'house_no' => $order->customer_address->house_no,
                        'address_line_1' => $order->customer_address->address_line1,
                        'address_line_2' => $order->customer_address->address_line2,
                        'city' => $order->customer_address->city->city_name,
                        'municipality' => $order->customer_address->municipality ? $order->customer_address->municipality->name : null,
                        'zipcode' => $order->customer_address->zipcode,
                        'name' => $order->customer_address->name,
                        'type' => $order->customer_address->type,
                        'landmark' => $order->customer_address->landmark,
                    ] : null,
                    'expected_delivery_date' => $order->delivery_date,
                    'items' => []
                ];   

                //echo "<pre>"; print_r($order); exit;            
                $itemsQuery =  $order['order_items'];
                // Format items data
                foreach ($itemsQuery as $item) {

                    $product_image = null;
                    $image = $this->ProductImages->getDefaultProductImage($item->product->id);
                    if ($image) {
                        $product_image = $this->Media->getCloudFrontURL($image);
                    }

                    $returnEligibilityDate = date('Y-m-d', strtotime("{$item->created} +{$siteSettings->product_return_in_days} days"));

                    $latestTrackingStatus = end($item->order_tracking_histories);

                    /*if ($latestTrackingStatus) {
                        $estimatedDeliveryDate = $latestTrackingStatus->status == 'Shipped'
                            ? date('Y-m-d', strtotime("{$latestTrackingStatus->updated} +5 days"))
                            : null;
                    }
                    */
                    $totalPrice = ($item->total_price > 0) ? $item->total_price : ($item->quantity * $item->price);

                    $orderData['items'][] = [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_name' => $item->product->name,
                        'product_image' => $product_image,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'total_price' => $totalPrice,
                        'latest_status' => $latestTrackingStatus->status ?? $item->status,
                        'status_updated_at' => $latestTrackingStatus->updated ?? $order->updated,
                        'product_variant' => $item->product_variant,
                        /*'estimated_delivery_date' => $estimatedDeliveryDate,*/
                        'return_replace_eligibility_date' => $returnEligibilityDate,
                        /*'tracking_history' => array_map(function ($tracking) {
                            return [
                                'status' => $tracking->status,
                                'comment' => $tracking->comment,
                                'updated_at' => $tracking->updated,
                            ];
                        }, $item->order_tracking_histories)*/
                    ];
                }
                $orderData['item_count'] = count($orderData['items']);
                $orderData['order_tracking_histories'] = $order['order_tracking_histories'];
                $orderData['customer'] = $order['customer'];
                $orderData['showroom'] = $order['showroom'];
                $orderData['offer'] = $order['offer'];
                $orderData['transactions'] = $order['transactions'][0];
                if( $order->showroom_id == $currentShowroomId && $order->from_showroom_id != $order->showroom_id &&!$order->showroom_approved )
                {
                    $orderData['showApproveButton'] = 1;          
                } else {
                    $orderData['showApproveButton'] = 0;
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $orderData,
                    'message' => __('Order details retrieved successfully')
                ];

                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }   

    //S Product
    public function productAutocomplete() {

        if ($this->request->is('get')) {

            // Get the search term from the query string
            $queryStr = $this->request->getQuery('q');

            if(!$queryStr){
                $result = [
                    'status' => __( 'error'),
                    'code' => 200,
                    'data' => (object)[],
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            
            $suggestions = $this->Products->productAutocomplete($queryStr);

            if(!empty($suggestions)){
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $suggestions
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __( 'error'),
                    'code' => 200,
                    'data' => $suggestions,
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
            }

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S search Product
    public function searchProduct() {

        if ($this->request->is('get')) {

            // search string OR product id
            $queryStr = $this->request->getQuery('q');            
            $flag = 'ID'; //STRING
            $products = $this->Products->searchProduct($flag, $queryStr);  
            
            foreach ($products as &$product) {

                //$product['rating'] = $this->Reviews->getAverageRating($product['id']);  
                //$product['total_review'] = $this->Reviews->getTotalReviews($product['id']);  
                $product['discount'] = $this->Products->getDiscount($product['id']);

                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {                    
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }

                // Loop through the product attributes
                /*foreach ($product['product_attributes'] as $productAttribute) {
                    // Extract attribute details
                    $attributeName = $productAttribute->attribute->name;
                    $attributeValue = $productAttribute->attribute_value->value;

                    // Store the attribute and its values in an associative array
                    if (!isset($uniqueAttributes[$attributeName])) {
                        $uniqueAttributes[$attributeName] = [];
                    }
                    if (!in_array($attributeValue, $uniqueAttributes[$attributeName])) {
                        $uniqueAttributes[$attributeName][] = $attributeValue;
                    }
                } */                   
            }
            unset($product);

            $result = ['status' => __('success'), 'data' => $products];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S add customer
    public function addCustomer() {

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData();
            $data['created_by'] = $userId;
            $data['user_type'] = 'Customer';
            $data['password'] = $this->Global->randomPassword();

            $exists = $this->Customers->Users->exists(['email' => $data['email']]); 
            if($exists){
                $result = ['status' => __('error'), 'message' => _('Email Already exists.')];
                goto label;
            }         

            $addUser = $this->Users->add_user($data);
            if($addUser){
                $user_id = $addUser;
                $addCust = $this->Customers->addCustomer($user_id, $data); 
            }
            if($addCust){
                $customer_id = $addCust;
                $addCustAddrs = $this->CustomerAddresses->addCustomerAddress($customer_id, $data);
            }
            
            //welcome email and sms
            if ($addCust) {
                if ($data['email'] !== '') {
                    $to = trim($data['email']);
                    $subject = "Welcome Email";
                    $template = "welcome_user";
                    $viewVars = ['username' => $data['first_name'] . ' ' . $data['last_name'], 'email' => $data['email'], 'password' => $data['password']];
                    $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
                }

                if ($data['mobile_no'] != '') {
                    $smsResult = $this->Global->sendWelcomeMsg($data['m_country_code'] . $data['mobile_no'], $data['password']);
                }

                $result = ['status' => __('success'), 'data' => ['customer_id' => $addCust, 'address_id' => $addCustAddrs], 'message' => __('The customer has been saved.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('error'), 'message' => __('The customer could not be saved. Please try again with another email ID.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S edit customer address
    public function updateCustomerAddress() {

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData(); 
            $id = $data['id'];  
            $editCustAddrs = $this->CustomerAddresses->editAddress($id, $data);            
            
            if ($editCustAddrs === false) {
                $result = ['status' => __('error'), 'message' => __('Address not found or could not be updated.')];                
            } else {            
                $result = ['status' => __('success'), 'data' => $editCustAddrs, 'message' => __('Address successfully updated.')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S add customer new address
    public function addCustomerAddress() {

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData(); 
            $customer_id = $data['customer_id'];  
            $addCustAddrs = $this->CustomerAddresses->addCustomerAddress($customer_id, $data);           
            
            if ($addCustAddrs) { 
                $data['id'] = $addCustAddrs;            
                $result = ['status' => __('success'), 'data' => $data, 'message' => __('Address successfully added.')];
                $this->response = $this->response->withStatus(200);                
            } else {            
                $result = ['status' => __('error'), 'message' => __('Address could not be added.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S customer
    public function customerAutocomplete() {

        if ($this->request->is('get')) {

            // Get the search term from the query string
            $queryStr = $this->request->getQuery('q');
            
            $suggestions = $this->Customers->customerAutocomplete($queryStr);

            if(!empty($suggestions)){
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $suggestions
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __( 'error'),
                    'code' => 200,
                    'data' => $suggestions,
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
            }

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S search customer
    public function searchCustomer() {

        if ($this->request->is('get')) {

            // search string OR customer id
            $queryStr = $this->request->getQuery('q');            
            $flag = 'ID'; //STRING
            
            $customers = $this->Customers->searchCustomer($flag, $queryStr);

            if(!empty($customers)){
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $customers
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __( 'error'),
                    'code' => 200,
                    'data' => $customers,
                    'message' => __('No results found')
                ];
                $this->response = $this->response->withStatus(200);
            }

        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    /////////// Start By Manisha ///////////////
    
    //M
    public function notificationSetting()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
            
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);  
                goto label;
            } else {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');
                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
                $emailNotifications = $this->request->getData('email_notification');
                $smsNotifications = $this->request->getData('sms_notification');
                $pushNotifications = $this->request->getData('push_notification');
                //$appNotifications = $this->request->getData('app_notification');
                // Find the user
                $user = $this->Users->get($userId);
                
                $user_attributes = [
                    'email_notifications' => $emailNotifications,
                    'sms_notification' => $smsNotifications,
                    'push_notification' => $pushNotifications,
                    //'app_notifications' => $appNotifications
                ];
                $user = $this->Users->update_user_by_id($userId, $user_attributes);
                if ($user) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => $user,
                        'message' => __('Notification preferences updated successfully'),
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to update notification preferences'),
                    ];
                    $this->response = $this->response->withStatus(500);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed'),
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function addExpense()
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');
                
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);  
                goto label;
                
            } else {
                $userId  = $identity->get('id');
                $user = $this->Users->get($userId);
                $data = $this->request->getData();
                $data['incurred_by'] = $userId;

                //upload cheque photo
                if (isset($data['cheque_photo']) && $data['cheque_photo']->getError() === UPLOAD_ERR_OK) {
                            
                    $cheque_photo = $data['cheque_photo'];
                    $fileName = trim($cheque_photo->getClientFilename());

                    if (!empty($fileName)) {
                        $imageTmpName = $cheque_photo->getStream()->getMetadata('uri');
                        $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                        $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                        $expenseFolder = Configure::read('Settings.EXPENSES');
                        
                        $folderPath = $uploadFolder.$expenseFolder;
                        $targetdir = WWW_ROOT . $folderPath;
                        $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                        $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                        $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                        if ($uploadResult !== 'Success') {
                            $result = [
                                'status' => __('error'),
                                'code' => 200,
                                'message' => __('Cheque photo could not be uploaded. Please, try again.')
                            ];
                            $this->response = $this->response->withStatus(200);
                            goto label;
                        } else {
                            $data['cheque_photo'] = $folderPath . $imageFile;
                        }
                    }
                } else {
                    $data['cheque_photo'] = '';
                }

                //
                $expenseId = $this->ShowroomExpenses->addExpense($data);

                if ($expenseId) {                    
                    $uploadedImages = !empty($this->request->getData('receipt')) ? $this->handleFileUploads($expenseId) : [];
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('The expense has been saved.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 400,
                        'message' => __('There was an error saving the expense.'),
                        //'errors' => $expense->getErrors()
                    ];
                    $this->response = $this->response->withStatus(400);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function editExpense($id = null)
    {
        if ($this->request->is(['patch', 'post', 'put'])) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            $expense = $this->ShowroomExpenses->get($id, [
                'contain' => ['ShowroomExpenseImages'],
            ]);
            $data = $this->request->getData();
            $data['incurred_by'] = $identity->get('id');

            //upload cheque photo
            if (isset($data['cheque_photo']) && $data['cheque_photo']->getError() === UPLOAD_ERR_OK) {
                        
                $cheque_photo = $data['cheque_photo'];
                $fileName = trim($cheque_photo->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $cheque_photo->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $expenseFolder = Configure::read('Settings.EXPENSES');
                    
                    $folderPath = $uploadFolder.$expenseFolder;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('Cheque photo could not be uploaded. Please, try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    } else {
                        $data['cheque_photo'] = $folderPath . $imageFile;
                    }
                }
            }

            $expense = $this->ShowroomExpenses->patchEntity($expense, $data);
            if ($this->ShowroomExpenses->save($expense)) {
                if (!empty($expense->showroom_expense_images)) {
                    foreach ($expense->showroom_expense_images as $image) {
                        $this->Media->awsDelete($image->image);
                        $this->ShowroomExpenses->ShowroomExpenseImages->delete($image);
                    }
                }
                if (!empty($this->request->getData('receipt'))) {
                    $this->handleFileUploads($expense->id);
                }
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('The expense has been updated.'),
                    'data' => $expense
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('There was an error updating the expense.'),
                    'errors' => $expense->getErrors()
                ];
                $this->response = $this->response->withStatus(400);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function deleteExpense($id = null)
    {
        if ($this->request->is(['post'])) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            $expense = $this->ShowroomExpenses->get($id, [
                'contain' => ['ShowroomExpenseImages'],
            ]);
            if (!empty($expense->showroom_expense_images)) {
                foreach ($expense->showroom_expense_images as $image) {
                    $this->Media->awsDelete($image->image);
                    $this->ShowroomExpenses->ShowroomExpenseImages->delete($image);
                }
            }
            if ($this->ShowroomExpenses->delete($expense)) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('The expense and its images have been deleted successfully.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('There was an error deleting the expense.'),
                    'errors' => $expense->getErrors()
                ];
                $this->response = $this->response->withStatus(400);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    private function handleFileUploads($expenseId)
    {
        $files = $this->request->getData('receipt');
        $uploadedImages = [];
        $i = 0;
        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());
                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.EXPENSES');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $expenseImage = $this->ShowroomExpenses->ShowroomExpenseImages->newEmptyEntity();
                        $expenseImage->showroom_expense_id = $expenseId;
                        $expenseImage->image = $folderPath . $imageFile;
                        $expenseImage->status = 'A';
                        $i++;
                        if ($this->ShowroomExpenses->ShowroomExpenseImages->save($expenseImage)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    //M (not used)
    public function recentExpenses() {
        if ($this->request->is(['post'])) {
            $identity = $this->request->getAttribute('identity');
            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            $data = $this->request->getData();
            $showroomId = $data['showroom_id'];
            if (!$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id is required')
                ];
                goto label;
            }
            
            $expenseLimit = 5;
            $recentExpenses = $this->ShowroomExpenses->getRecentExpenses($showroomId, $expenseLimit);
            $result = [
                'status' => 'success',
                'code' => 200,
                'message' => __('Recent expenses listed successfully'),
                'data' => $recentExpenses
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function expenseDashboard() {

        if ($this->request->is('post')) {
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');
                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             
            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'];
            $period = $data['period'];
            $year = isset($data['year']) ? $data['year'] : date('Y');
            if (!$period || !$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period (Day, Month, or Year) are required')
                ];
                goto label;
            }
            
            //period Day
            $financialData = $this->expenseForDay($showroomId, $year);
            $expensesSummary = $this->getExpensesSummarywithYear('day', $showroomId, $year);            

            //recent expenses
            $expenseLimit = 5;
            $recentExpenses = $this->ShowroomExpenses->getRecentExpenses($showroomId, $expenseLimit);

            $result = ['status' => __('success'), 'data' => ['expenseData'=>$financialData, 'expenseSummary'=>$expensesSummary, 'recentExpenses'=>$recentExpenses]];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function expenseGraphFilter() {

        if ($this->request->is('post')) {
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');
                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             
            $data = $this->request->getData();            
            $showroomId = $data['showroom_id'];
            $period = $data['period'];
            $year = isset($data['year']) ? $data['year'] : date('Y');
            if (!$period || !$showroomId) {
                $result = [
                    'status' => 'error',
                    'message' => __('Valid showroom_id and period (Day, Month, or Year) are required')
                ];
                goto label;
            }
            if($period == 'Day') {
                $financialData = $this->expenseForDay($showroomId, $year);
                $expensesSummary = $this->getExpensesSummarywithYear('day', $showroomId, $year);
            } else if($period == 'Month') {
                $financialData = $this->expenseForMonth($showroomId, $year);
                $expensesSummary = $this->getExpensesSummarywithYear('month', $showroomId, $year);
            } else if ($period == 'Year') {
                $financialData = $this->expenseForYear($showroomId, $year);
                $expensesSummary = $this->getExpensesSummarywithYear('year', $showroomId, $year);
            } else { }

            $result = ['status' => __('success'), 'data' => ['expenseData'=>$financialData, 'expenseSummary'=>$expensesSummary]];
            $this->response = $this->response->withStatus(200);
                                  
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    private function expenseForDay($showroomId, $year)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subDays(6);          // 7 days ago (including today)
        // Query to fetch expense for each of the last 7 days
        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'date' => 'DATE(incurred_date)',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'YEAR(incurred_date)' => $year,
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE(incurred_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();
            
        // Process the result to ensure each day in the range has a value
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = $endDate->subDays($i)->format('Y-m-d');
            $expense = 0;
            foreach ($expenseQuery as $row) {
                if ($row['date'] === $date) {
                    $expense = $row['total_expense'];
                    break;
                }
            }
            $data[] = [
                'date' => $date,
                'expense' => $expense
            ];
        }
        return $data;
    }

    //M
    private function expenseForMonth($showroomId, $year)
    {
        $endDate = FrozenDate::now();                 // Today's date
        $startDate = $endDate->firstOfMonth();        // First day of the current month
        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'date' => 'DATE(incurred_date)',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'YEAR(incurred_date)' => $year,
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE(incurred_date)')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();
        // Process the result to ensure each day in the month has a value
        $data = [];
        $currentDate = $startDate;
        // Loop through all days of the current month
        while ($currentDate <= $endDate) {
            $date = $currentDate->format('Y-m-d');
            $expense = 0;
            foreach ($expenseQuery as $row) {
                if ($row['date'] === $date) {
                    $expense = $row['total_expense'];
                    break;
                }
            }
            $data[] = [
                'date' => $date,
                'expense' => $expense
            ];
            // Move to the next day
            $currentDate = $currentDate->addDays(1);
        }
        return $data;
    }

    //M
    private function expenseForYear($showroomId, $year)
    {
        $endDate = FrozenDate::now();               // Today's date
        $startDate = $endDate->subMonths(5)->firstOfMonth();  // First day of the month, 6 months ago
        // Query to fetch expense for each month within the last 6 months
        $expenseQuery = $this->ShowroomExpenses->find()
            ->select([
                'month' => 'DATE_FORMAT(incurred_date, "%Y-%m")',             // Group by date
                'total_expense' => 'SUM(amount)'    // Sum of expense per day
            ])
            ->where([
                'DATE(incurred_date) >=' => $startDate->format('Y-m-d'),
                'DATE(incurred_date) <=' => $endDate->format('Y-m-d'),
                'YEAR(incurred_date)' => $year,
                'status' => 'A',
                'showroom_id' => $showroomId
            ])
            ->group('DATE_FORMAT(incurred_date, "%Y-%m")')
            ->enableHydration(false)                // Return as an array, not entity objects
            ->toArray();
        // Process the result to ensure each month in the range has a value
        $data = [];
        $currentMonth = $startDate;
        // Loop through all months within the last 6 months
        while ($currentMonth <= $endDate) {
            $month = $currentMonth->format('Y-m');
            $expense = 0;
            foreach ($expenseQuery as $row) {
                if ($row['month'] === $month) {
                    $expense = $row['total_expense'];
                    break;
                }
            }
            $data[] = [
                'month' => $month,
                'expense' => $expense
            ];
            // Move to the next month
            $currentMonth = $currentMonth->addMonths(1);
        }
        return $data;
    }

    //M
    private function getExpensesSummarywithYear($type, $showroomId, $year)
    {
        $query = $this->ShowroomExpenses->find();
        // Select total and average expenses
        $result = $query->select([
            'total_expenses' => $query->func()->sum('ROUND(amount, 2)'), // Total sum of the expenses
            'average_expenses' => $query->func()->avg('ROUND(amount, 2)'), // Average of the expenses
        ]);
        // Add conditions based on the time type
        if ($type === 'day') {
            // Last 7 days
            $startDate = date('Y-m-d', strtotime('-7 days'));
            $endDate = date('Y-m-d');
            $result->where(function ($exp) use ($startDate, $endDate) {
                return $exp->between('incurred_date', $startDate . ' 00:00:00', $endDate . ' 23:59:59');
            });
        } elseif ($type === 'month') {
            // Current month of the specified year
            $currentMonth = $year . '-' . date('m');
            $result->where(function ($exp) use ($currentMonth) {
                return $exp->like('incurred_date', $currentMonth . '%'); // Match 'YYYY-MM' format
            });
        } elseif ($type === 'year') {
            // Entire year specified
            $startDate = $year . '-01-01';
            $endDate = $year . '-12-31';
            $result->where(function ($exp) use ($startDate, $endDate) {
                return $exp->between('incurred_date', $startDate . ' 00:00:00', $endDate . ' 23:59:59');
            });
        }
        // Add showroom condition
        $result->where(['showroom_id' => $showroomId]);
        $data = $result->first();
        // Handle case where no expenses exist
        if (!$data || $data->total_expenses === null) {
            return [
                'total_expenses' => 0,
                'average_expenses' => 0,
            ];
        }
        return $data;
    }

    //M
    public function listExpenseCategories(){
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }             
            $categoriesQuery = $this->ShowroomExpenseCategories->getAllExpenseCategories();
            $categoriesArray = $categoriesQuery->toArray();
            if (empty($categoriesArray)) {
                $result = [
                    'status' => __('error'),
                    'message' => __('No expense category found')
                ];
                $this->response = $this->response->withStatus(200); 
            } else {
                $result = [
                    'status' => __('success'),
                    'data' => $categoriesArray,
                    'message' => __('Expenses categories listed successfully.')
                ];
                $this->response = $this->response->withStatus(200); 
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //M
    public function listExpenses()
    {
        if ($this->request->is('get')) {
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }         
            $query = $this->ShowroomExpenses->find()
            ->contain([
                'ShowroomExpenseCategories' => [
                    'fields' => ['id', 'name'] // Select specific fields from expense_categories
                ],
                'IncurredByUsers' => [
                    'fields' => ['id', 'first_name', 'last_name'] // Select specific fields from users
                ]
            ]);

            //search expense by name
            $search_str = $this->request->getQuery('search_str');
            if($search_str){
                $query->where(['ShowroomExpenses.name LIKE' => '%' . $search_str . '%']);
            }

            // Apply filters if available
            $date = $this->request->getQuery('date');
            if ($date && preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                $query->where(['DATE(incurred_date)' => $date]);
            } elseif ($date) {
                $this->response = $this->response->withStatus(400);
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Invalid date format')
                ];
                goto label;
            }
            if ($this->request->getQuery('payment_status') !== null) {
                $query->where(['payment_status' => $this->request->getQuery('payment_status')]);
            }
            
            $time_period = $this->request->getQuery('time_period'); // Change this to 'month', 'quarter', or 'year' based on your requirement.
            if( $time_period) {
                switch ($time_period) {
                    case 'month':
                        // Start and end of the current month
                        $startDate = (new \DateTime('first day of this month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of this month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'quarter':
                        // Determine the current quarter
                        $quarter = ceil(date('n') / 3);
                        $firstMonthOfQuarter = ($quarter - 1) * 3 + 1;
                        $lastMonthOfQuarter = $quarter * 3;

                        // Start and end of the current quarter
                        $startDate = (new \DateTime('first day of ' . $firstMonthOfQuarter . ' month'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of ' . $lastMonthOfQuarter . ' month'))->format('Y-m-d 23:59:59');
                        break;

                    case 'year':
                        // Start and end of the current year
                        $startDate = (new \DateTime('first day of January'))->format('Y-m-d 00:00:00');
                        $endDate = (new \DateTime('last day of December'))->format('Y-m-d 23:59:59');
                        break;

                    default:
                        $this->response = $this->response->withStatus(400);
                        $result = [
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('Invalid time period')
                        ];
                        goto label;
                }
                $query->where([
                        'incurred_date >=' => $startDate,
                        'incurred_date <=' => $endDate,
                    ]);
            }

           /* $month = $this->request->getQuery('month'); //2024-12
            if ($month && preg_match('/^\d{4}-\d{2}$/', $month)) {
                $query->where(function ($exp) use ($month) {
                    return $exp->like('incurred_date', $month . '%');
                });
            } elseif ($month) {
                $this->response = $this->response->withStatus(400);
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Invalid month format')
                ];
                goto label;
            }*/


            if ($this->request->getQuery('category')) {
                $query->where(['expense_category_id' => $this->request->getQuery('category')]);
            }
            // Add showroom condition if necessary
            $showroomId = $this->request->getQuery('showroom_id');
            if ($showroomId) {
                $query->where(['showroom_id' => $showroomId]);
            }
            $query->where(['ShowroomExpenses.status !=' => 'D']);
            // echo "<pre>"; print_r($query); die;
            $query->order(['ShowroomExpenses.created' => 'DESC']);
            $expenses = $query->all()->toArray();

            foreach ($expenses as $ke => $ve) {
                if($ve['status'] == 'A'){
                    $expenses[$ke]['status'] ='Active';
                } elseif($ve['status'] == 'I') {
                    $expenses[$ke]['status'] = 'Inactive';
                }
            }
            $result = [
                'status' => __('success'),
                'data' => $expenses,
                'message' => __('Expenses listed successfully.')
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S view expense
    public function viewExpense() {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getQuery();
            $expense_id = $data['id'];

            $detail = $this->ShowroomExpenses->viewExpenseDetail($expense_id); 

            if (!empty($detail["cheque_photo"])) {                
                $detail["cheque_photo"] = $this->Media->getCloudFrontURL($detail["cheque_photo"]);
            } 

            // Append full URL to showroom_expense_images
            if (!empty($detail["showroom_expense_images"])) {
                foreach ($detail["showroom_expense_images"] as &$val) {
                    $val["image"] = $this->Media->getCloudFrontURL($val["image"]);                     
                }
            }        
            
            if ($detail) {
                $result = ['status' => __('success'), 'data' => $detail];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    //S recordPayment
    public function updateExpensePayment() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getData();
            $expense_id = $data['id'];

            $update = $this->ShowroomExpenses->updateExpensePayment ($expense_id, $data);         
            
            if ($update) {
                $result = ['status' => 'success', 'message' => __('Updated successfully')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to update')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    /////////// End By Manisha ///////////////

    //S order item return
    public function productReturn($orderItemId = null)
    {
        if ($this->request->is('post')) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }            
            
            $roleId  = $identity->get('role_id');
            $userId  = $identity->get('id');
            // Load the role name based on the role_id
            $role = $this->Users->Roles->get($roleId);
            $roleName = $role->name;

            $data = $this->request->getData();
            $customerId = $data['customer_id'];
            $cust_user_id = $data['user_id'];
            $user = $this->Users->get($cust_user_id);

            if (empty($orderItemId)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Order item ID is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            try {
                $orderItem = $this->OrderItems->get($orderItemId, [
                    'contain' => ['Products']
                ]);
            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Order item not found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $order = $this->Orders->get($orderItem['order_id']);
            if ($order->customer_id != $customerId) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Unauthorized to return this order')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if ($orderItem['status'] !== 'Delivered') {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Only delivered items can be returned')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $existingReturn = $this->OrderReturns->find()
                ->where(['order_item_id' => $orderItemId])
                ->first();

            if ($existingReturn) {
                switch ($existingReturn->status) {
                    case 'Pending':
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('A return request has already been initiated and is awaiting approval.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;

                    case 'Approved':
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('A return request has already been approved and is in progress.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;

                    case 'Completed':
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('This order item has already been returned.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;

                    default:
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('This order item has an existing return request with status: ' . $existingReturn->status)
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                }
            }
           
            $returnData = [
                'order_item_id' => $orderItemId,
                'order_return_category_id' => $data['reason_id'],
                'reason' => $data['reason'] ?? null,
                'status' => 'Pending',
                'requested_at' => date('Y-m-d H:i:s'),
                'return_amount' => 100.00
            ];

            $orderItem->status = 'Pending Return';

            if ($this->OrderItems->save($orderItem)) {

                $returnId = $this->OrderReturns->add_record($returnData);

                if ($returnId) {

                    $files = $data['images'] ?? [];
                    $uploadError = false;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            if ($file->getError() === UPLOAD_ERR_OK) {
                                $fileName = trim($file->getClientFilename());

                                if (!empty($fileName)) {
                                    $imageTmpName = $file->getStream()->getMetadata('uri');

                                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                    $filePath = Configure::read('Settings.PRODUCT_RETURN');
                                    $folderPath = $uploadFolder . $filePath;
                                    $targetDir = WWW_ROOT . $folderPath;
                                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                    $uploadResult = $this->Media->upload($imageTmpName, $targetDir, $imageFile, $folderPath);
                                    if ($uploadResult === 'Success') {
                                        $imageData = [
                                            'order_return_id' => $returnId,
                                            'image_url' => $folderPath . $imageFile
                                        ];

                                        if (!$this->OrderReturnImages->add_record($imageData)) {
                                            $uploadError = true;
                                        }
                                    } else {
                                        $uploadError = true;
                                    }
                                }
                            }
                        }
                    }

                    $trackingData = [
                        'order_item_id' => $orderItemId,
                        'status' => 'Pending Return',
                        'comment' => $data['comment'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->newEntity($trackingData);
                    $this->OrderTrackingHistories->save($tracking);


                    $category =  $this->OrderReturnCategories->find()
                                ->select(['name'])
                                ->where(['id' => $data['reason_id']])
                                ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Item Return Request';
                    $template = 'order_item_return';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'order_item_product' => $orderItem['product']['name'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'status' => 'pending',
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'requested_at' => $returnData['requested_at'],
                    ];

                    $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

                    if($sendEmail) {
                        $result = [
                            'status' => __('success'),
                            'code' => 200,
                            'message' => __('Order item return has been initiated successfully, and an email notification has been sent to the admin.')
                        ];
                    } else {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('Order item return has been initiated successfully, but the email notification could not be sent to the admin.')
                        ];

                    }
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Failed to process the return')
                    ];
                    $this->response = $this->response->withStatus(200);
                }

            } else {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Failed to cancel the order')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function orderReturnCategoriesList()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->OrderReturnCategories->getAllCategories();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => __('error'),
                    'message' => __('No return categories found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __('success'),
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }       

    //S order change status
    public function changeOrderStatus() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData();
            $order_id = $data['id'];           
            $data['status'] = 'Shipped';
            $data['last_modified_by'] = $userId;            

            $changeStatus = $this->Orders->changeStatus($order_id, $data);
            
            $trackingData = [
                    'order_id' => $order_id,
                    'status' => 'Shipped',
                    'comment' => $data['comment'] ?? null,
                ];
            $tracking = $this->OrderTrackingHistories->newEntity($trackingData);
            $this->OrderTrackingHistories->save($tracking);
            
            if ($changeStatus) {
                $result = ['status' => 'success', 'message' => __('Status changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to change status. Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S order delete
    public function deleteOrder($id) {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $order = $this->Orders->get($id);             
            if ($order) {
                if($order->shipment_status != 'Unassigned') {
                    $result = ['status' => 'error', 'message' => __('Order deletion is not allowed as a shipment has already been created.')];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $options = ['last_modified_by' => $userId];
                    //if ($this->Orders->delete($order)) {
                    if ($this->Orders->delete_order($order, $options)) {    
                        $result = ['status' => 'success', 'message' => __('The order has been marked as Deleted.')];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = ['status' => 'error', 'message' => __('The order could not be deleted. Please, try again.')];
                        $this->response = $this->response->withStatus(200);
                    }  
                }              
            } else {            
                $result = ['status' => 'error', 'message' => __('The order does not exist.')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S
    public function orderCancellationCategoriesList()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->OrderCancellationCategories->getAllCategories();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => __('error'),
                    'message' => __('No cancellation categories found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => __('success'),
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S
    public function cancelOrder($orderId = null)
    {
        $result = [];

        if ($this->request->is('post')) {
           
           // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }            

            $user = $this->Users->get($userId);

            if (empty($orderId)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Order ID is required')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            try {
                $order = $this->Orders->get($orderId, [
                    'contain' => ['OrderItems']
                ]);
            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Order not found')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $customerId = $order->customer_id;

            if($order->shipment_status != 'Unassigned') {

                $result = ['status' => 'error', 'message' => __('Order cancellation is not allowed as a shipment has already been created.')];
                $this->response = $this->response->withStatus(200);
                goto label;
            } 

          /*if ($order->customer_id !== $customerId) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Unauthorized to cancel this order')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }*/

            if ($order->payment_method == 'Credit') {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('The order cannot be canceled as it was made on credit')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $anyDelivered = false;
            foreach ($order->order_items as $item) {
                if ($item->status === 'Shipped') {
                    $anyDelivered = true;
                    break;
                }

                if ($item->status === 'Delivered') {
                    $anyDelivered = true;
                    break;
                }
            }

            if ($anyDelivered) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $existingCancellation = $this->OrderCancellations->find()
                ->where(['order_id' => $orderId])
                ->first();

            if ($existingCancellation) {
                if ($existingCancellation->status === 'Pending') {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Cancellation has already been initiated')
                    ];
                } elseif ($existingCancellation->status === 'Approved') {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Cancellation has already in progress')
                    ];
                } elseif ($existingCancellation->status === 'Completed') {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('This order has already been cancelled')
                    ];
                }
                $this->response = $this->response->withStatus(200);
                goto label;

                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('This order has already been cancelled')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $data = $this->request->getData();

            $cancellationData = [
                'order_id' => $orderId,
                'order_cancellation_category_id' => $data['reason_id'],
                'customer_id' => $customerId,
                'reason' => $data['reason'] ?? null,
                'status' => 'Pending',
                'canceled_at' => date('Y-m-d H:i:s')
            ];

            $order->status = 'Pending Cancellation';
            if ($this->Orders->save($order)) {

                $cancellation = $this->OrderCancellations->add_record($cancellationData);

                if ($cancellation) {

                    $trackingData = [
                        'order_id' => $orderId,
                        'status' => 'Pending Cancellation',
                        'comment' => $data['reason'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->add_record($trackingData);

                    $category = $this->OrderCancellationCategories->find()
                        ->select(['name'])
                        ->where(['id' => $data['reason_id']])
                        ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Cancellation Request';
                    $template = 'order_cancellation';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'canceled_at' => $cancellationData['canceled_at'],
                    ];

                    // $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
                    $sendEmail = $this->WebsiteFunction->orderCancellationRequestEmail($data['order_item_id'], $data['reason_id'], $data['reason'], date('Y-m-d H:i:s'));

                    if ($sendEmail) {
                        $result = [
                            'status' => __('success'),
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, and an email notification has been sent to the admin.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('Order cancelled has been initiated successfully, but the email notification could not be sent.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Failed to cancel the order')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Failed to cancel the order')
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S add order
    public function addOrder() {

        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');            
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
            }

            $data = $this->request->getData();

            if($roleName == 'Showroom Manager') {
                $cond = ['Showrooms.showroom_manager'=>$userId];
                $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                $data['from_showroom_id'] = $showroom['id'];
            }

            if(!$data['order_type']) {
              $data['order_type'] = 'Showroom';  
            }
            //$data['delivery_mode_type'] = 'standard';
            
            $data['order_online_source'] = 'Mobile';
            $data['created_by'] = $userId;
            $data['created_by_role'] = $roleId;
            
            // Check if sales_person_id is passed from request (e.g., via dropdown)
            $passedSalesPersonId = $data['sales_person_id'] ?? null;

            if (!empty($passedSalesPersonId)) {
                $data['sales_person_id'] = (int)$passedSalesPersonId;
            } elseif ($roleName === 'Sales Person') {
                // Auto-assign logged-in user if they are a Sales Person
                $data['sales_person_id'] = $userId;
            } else {
                $data['sales_person_id'] = null;
            }

            $data['status_date'] = date('Y-m-d H:i:s');
            $data['order_date'] = date('Y-m-d H:i:s');
            $data['status'] = "Pending"; 

            if ($data['delivery_mode'] == 'delivery') {
                $data['delivery_date'] = $this->CalculateEstimatedDelivery($data['delivery_mode_type']);
            }

            //
            //$paymentMethod = strtolower($data['payment_method']);
            $paymentMethod = $data['payment_method'];

            if( in_array($paymentMethod, ['Cash on Delivery', 'COD', 'Cash'])) {

                $deliveryCityId = $data['city_id'];
            $codCheck = $this->Global->validateCODAvailability($data['order_items'], $deliveryCityId);
                if (!$codCheck['cod_available']) {
                    $result = [
                        'status' => 'error',
                    'message' => $codCheck['message'],
                        'data' => $codCheck['non_cod_products']
                    ];
                    goto label;
                }
            }          
            //upload cheque photo
            if (isset($data['cheque_photo']) && $data['cheque_photo']->getError() === UPLOAD_ERR_OK) {
                        
                $cheque_photo = $data['cheque_photo'];
                $fileName = trim($cheque_photo->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $cheque_photo->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $orderFolder = Configure::read('Settings.ORDERS');
                    
                    $folderPath = $uploadFolder.$orderFolder.'payment/';
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('Cheque photo could not be uploaded. Please, try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    } else {
                        $data['cheque_photo'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['cheque_photo'] = '';
            }
            if(!$data['transaction_fee']){
                $data['transaction_fee'] = 0.00;
            }
            $order = $this->Orders->newEmptyEntity();
            $transactionData = [
                'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
                'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                'transaction_date' => date('Y-m-d H:i:s'),
                'amount' => $data['total_amount'],
                'transaction_fee' => $data['transaction_fee'],
                'payment_method' => $data['payment_method'],
                'payment_status' => 'Paid',
            ];
            $data['transactions'] = [$transactionData];
            $order = $this->Orders->patchEntity($order, $data, [
                'associated' => [
                   'OrderItems' => [
                        'associated' => ['OrderItemAttributes']
                    ], 
                    'Transactions'
                ]
            ]);
            $save_order = $this->Orders->save($order);              

            if ($save_order) {

                if($data['customer_id']){
                    $cust_data = $this->Customers->find()->select([])->contain(['Users'])->where(['Customers.id'=>$data['customer_id']])->first()->toArray();

                    //send whatsapp text
                    if($cust_data['user']['mobile_no']) {
                        $mobile_no = $cust_data['user']['country_code'].$cust_data['user']['mobile_no'];
                        $message_text = __("Order has been placed successfully");
                        $this->Global->sendWhatsAppMessage($mobile_no, $message_text);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $order,
                    'message' => __('Your order has been successfully created.')
                ]; 
                $this->response = $this->response->withStatus(200);
            } else {
                $validationErrors = $order->getErrors();
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'errors' => $validationErrors,
                    'message' => __('There was an issue creating your order. Please try again.')
                ];
                $this->response = $this->response->withStatus(200);
            } 
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S edit order
    public function editOrder() {

        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
            }

            $data = $this->request->getData();
            $orderId = $data['order_id'];
            
            //upload cheque photo
            if (isset($data['cheque_photo']) && $data['cheque_photo']->getError() === UPLOAD_ERR_OK) {
                        
                $cheque_photo = $data['cheque_photo'];
                $fileName = trim($cheque_photo->getClientFilename());

                if (!empty($fileName)) {
                    $imageTmpName = $cheque_photo->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $orderFolder = Configure::read('Settings.ORDERS');
                    
                    $folderPath = $uploadFolder.$orderFolder.'payment/';
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('Cheque photo could not be uploaded. Please, try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    } else {
                        $data['cheque_photo'] = $folderPath . $imageFile;
                    }
                }
            } else {
                $data['cheque_photo'] = '';
            }
            
            $order = $this->Orders->get($orderId, [
                'contain' => ['OrderItems', 'Transactions']
            ]);
            $data['last_modified_by'] = $userId;
           /* $transactionData = [
                'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
                'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
                'transaction_date' => date('Y-m-d H:i:s'),
                'amount' => $data['total_amount'],
                'payment_method' => $data['payment_method'],
                'payment_status' => 'Paid',
            ];
            $data['transactions'] = [$transactionData];*/
            
            $this->Orders->patchEntity($order, $data, [
                'associated' => [
                    'OrderItems' => [
                        'accessibleFields' => ['id' => true],
                        'replace' => true //this will remove missing ones
                    ]/*,
                    'Transactions'*/
                ]
            ]);
            $save_order = $this->Orders->save($order);              

            // echo "<pre>"; print_r($order); die;
            if ($save_order) {

                if($data['customer_id']){
                    $cust_data = $this->Customers->find()->select([])->contain(['Users'])->where(['Customers.id'=>$data['customer_id']])->first()->toArray();

                    //send whatsapp text
                    if($cust_data['user']['mobile_no']) {
                        $mobile_no = $cust_data['user']['country_code'].$cust_data['user']['mobile_no'];
                        $message_text = __("Order has been updated successfully");
                        $this->Global->sendWhatsAppMessage($mobile_no, $message_text);
                    }
                }

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => $order,
                    'message' => __('Your order has been successfully updated.')
                ]; 
                $this->response = $this->response->withStatus(200);
            } else {
                $validationErrors = $order->getErrors();
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'errors' => $validationErrors,
                    'message' => __('There was an issue updating your order. Please try again.')
                ];
                $this->response = $this->response->withStatus(200);
            } 
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', 'result');
    }

    //S scan product
    public function scanProduct() {

        if ($this->request->is('get')) {

            $code = $this->request->getQuery('code');            
            
            $product = $this->Products->getProductDetail($code);

            if($product){
                //$product['rating'] = $this->Reviews->getAverageRating($product['id']);  
                //$product['total_review'] = $this->Reviews->getTotalReviews($product['id']);  
                $product['discount'] = $this->Products->getDiscount($product['id']);

                $product['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($product['id']);
                if ($image) {                    
                    $product['product_image'] = $this->Media->getCloudFrontURL($image);
                }

                $result = ['status' => 'success', 'data' => $product];
                $this->response = $this->response->withStatus(200); 
            } else{
                $result = ['status' => 'success', 'data' => (object)[], 'message' => __('No data found')];
            }            
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }
    

    //S refund
    public function refund() {
        
    }  

    //S apply coupon
    public function applyCoupon_OLD()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
            }

            $data = $this->request->getData();
            $couponCode = $data['coupon_code'];
            $subTotal   = $data['sub_total'];
            $showroomId = $data['showroom_id'];
            $orderDate  = date('Y-m-d H:i:s');

            $offer = $this->Offers->applyCoupon($couponCode, $subTotal, $orderDate);

            if ($offer) {
                $validShowroom = true;
                if (!empty($offer->offer_showrooms)) {
                    $validShowroom = false;
                    foreach ($offer->offer_showrooms as $offerShowroom) {
                        if ($offerShowroom->showroom_id == $showroomId && $offerShowroom->status == 'A') {
                            $validShowroom = true;
                            break;
                        }
                    }
                }
                if ($validShowroom) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            'offer_amount' => $offer->discount,
                            'offer_type' => $offer->offer_type,
                            'min_cart_value' => $offer->min_cart_value
                        ],
                        'message' => __('Coupon applied successfully!')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This coupon is not valid for the selected showroom.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Invalid or expired coupon, or minimum order value not met.')
                ];
                $this->response = $this->response->withStatus(200);
            }            
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S apply coupon
    public function applyCoupon()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');
            if($identity) {
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                $roleName = $identity->get('_matchingData')['Roles']['name'];
            }

            $data = $this->request->getData();
            $couponCode = $data['coupon_code'];
            $subTotal   = $data['sub_total'];
            $showroomId = $data['showroom_id'];
            $customerId = $data['customer_id'];
            $productIds = $data['product_ids'];
            $sqlDate  = date('Y-m-d H:i:s');

            //get customer groups
            $customergroups = $this->CustomerGroupMappings->find()->select(['customer_group_id'])->where(['CustomerGroupMappings.status' => 'A', 'CustomerGroupMappings.customer_id' => $customerId])->toArray();
            if($customergroups) {
                foreach ($customergroups as $key => $value) {
                    $customer_groups[] = $value['customer_group_id'];
                }
            } else {
                $customer_groups = [];
            }           
         
            $offer = $this->Offers->find()
                ->where([ 'offer_code' => $couponCode, 'redeem_mode IN' => ['Store', 'Both'] ])
                ->contain([
                    'OfferShowrooms' => function ($q) {
                        return $q->where(['OfferShowrooms.status' => 'A']);
                    },
                    'OfferCategories' => function ($q) {
                        return $q->where(['OfferCategories.status' => 'A']);
                    },
                    'OfferCustomerGroups' => function ($q) {
                        return $q->where(['OfferCustomerGroups.status' => 'A']);
                    }
                ])
                ->first();

            if (!$offer) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Invalid coupon code.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            if ($offer->status !== 'A') {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This coupon is not active.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if ($offer->offer_start_date > $sqlDate) {                
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This coupon is not valid yet.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            if (!empty($offer->offer_end_date) && $offer->offer_end_date < $sqlDate) {                
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('This coupon has expired.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }
            if (!empty($offer->offer_showrooms)) {
                $validShowroom = false;
                foreach ($offer->offer_showrooms as $offerShowroom) {
                    if ($offerShowroom->showroom_id == $showroomId) {
                        $validShowroom = true;
                        break;
                    }
                }
                if (!$validShowroom) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('This coupon is not valid for the selected showroom.')
                    ];
                    $this->response = $this->response->withStatus(200); 
                    goto label;                   
                }                
            }
            if ($subTotal < $offer->min_cart_value) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Minimum cart value of ' . $offer->min_cart_value . ' is required.')
                    ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (!empty($customer_groups) && !empty($offer->offer_customer_groups)) {
                $validCustomerGroup = false;
                foreach ($offer->offer_customer_groups as $offerCustomerGroup) {
                   
                    if (in_array($offerCustomerGroup->customer_group_id, $customer_groups)) {
                        
                        $validCustomerGroup = true;
                        break;
                    }
                }
                if (!$validCustomerGroup) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('This coupon is not valid for your customer group.')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $validProducts = [];
            if (!empty($productIds) && !empty($offer->offer_categories)) {
                foreach ($productIds as $productId) {
                    foreach ($offer->offer_categories as $offerCategory) {
                        if ($this->Products->ProductCategories->exists([
                            'ProductCategories.product_id'  => $productId,
                            'ProductCategories.category_id' => $offerCategory->category_id,
                        ])) {
                            $validProducts[] = $productId;
                        }
                    }
                }

                if (empty($validProducts)) {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Selected products do not match the coupon categories.')
                    ];
                    $this->response = $this->response->withStatus(200);
                    goto label;
                }
            }

            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    'id' => $offer->id,
                    'offer_amount' => $offer->discount,
                    'offer_type' => $offer->offer_type,
                    'min_cart_value' => $offer->min_cart_value,
                    'free_shipping' => $offer->free_shipping,
                    'cat_products' => $validProducts,
                    'max_allowed' => $offer->max_amt_per_disc_value
                ],
                'message' => __('Coupon applied successfully!')
            ];
            $this->response = $this->response->withStatus(200);
                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S redeem Loyalty Point
    public function redeemLoyaltyPoint()
    {
        $identity = $this->request->getAttribute('identity');
        if($identity) {
            $roleId  = $identity->get('role_id');
            $userId  = $identity->get('id');
            $roleName = $identity->get('_matchingData')['Roles']['name'];
        }

        if ($this->request->is('post')) {
           
            $data = $this->request->getData();
            $customerId = $data['customer_id'];
            $redeemPoints    = isset($data['redeem_points']) ? (float)$data['redeem_points'] : 0; 
            $loyaltyCategory = isset($data['loyalty_category']) ? $data['loyalty_category'] : '';
            $loyaltyPoints   = isset($data['loyalty_points']) ? $data['loyalty_points'] : 0;           

           /* $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
            if($loyaltyDetails['status'] == 'error') {
                $result = $loyaltyDetails;
                goto label;
            } else {
                $loyaltyPoints  = $loyaltyDetails['data']['points'];
                $loyaltyCategory = $loyaltyDetails['data']['loyalty_category'];
                if(!$loyaltyPoints){
                     $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('No loyalty points found.')
                    ];
                }
                goto label;
            }*/            

            if ($loyaltyPoints < $redeemPoints) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Redeem points should be less than or equal to your available points.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $loyaltyPointsWorth = 0.00;
                $Loyalty_Redeem = Configure::read('Constants.REDEEM_LOYALTY');

                if ($loyaltyCategory === 'Standard') {
                    $loyaltyPointsWorth = $redeemPoints * floatval($Loyalty_Redeem['Standard']);
                } elseif ($loyaltyCategory === 'VIP') {
                    $loyaltyPointsWorth = $redeemPoints * floatval($Loyalty_Redeem['VIP']);
                }

                $loyaltyRecord = $this->Loyalty->find()->where(['customer_id' => $customerId])->first();

                if ($loyaltyRecord) {
                    $loyaltyRecord->points -= $redeemPoints;
                    $loyaltyRecord->spent_amount += $loyaltyPointsWorth;

                    if ($this->Loyalty->save($loyaltyRecord)) {
                        $result = [
                            'status' => __('success'),
                            'code' => 200,
                            'data' => [
                                'loyalty_points_worth' => number_format($loyaltyPointsWorth, 2),
                                'loyalty_points_remaining' => $loyaltyRecord->points
                            ],
                            'message' => __('Loyalty points redeemed successfully.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to update loyalty points. Please try again.')
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Loyalty record not found.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }        
        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S (Not Required)
    public function showroomCharge() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $showroomId = $this->request->getData('showroom_id');
            $deliveryMode = 'showroom';
            $weightQuantityArray = $this->request->getData('weightQuantityArray');

            if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {              
                $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Invalid weight and quantity data.')
                ];
                goto label;
            }

            $showroom = $this->Showrooms->find()
                ->select(['city_id'])
                ->where(['id' => $showroomId])
                ->first();

            if ($showroom) {
                $cityId = $showroom->city_id;
                $deliveryCharges = $this->DeliveryCharges->find()
                    ->where([
                        'city_id' => $cityId,
                        'delivery_mode' => $deliveryMode,
                        'status' => 'A'
                    ])
                    ->order(['weight' => 'ASC'])
                    ->toArray();

                $total_delivery_charge = 0.00;

                if (empty($deliveryCharges)) {                    
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'data' => [
                            'delivery_charge' => $total_delivery_charge
                        ],
                        'message' => __('No delivery charges found for the selected criteria.')
                        ];
                    goto label;
                }                

                foreach ($weightQuantityArray as $item) {
                    $weight = $item['weight'];
                    $quantity = $item['quantity'];
                    $applicableCharge = null;

                    foreach ($deliveryCharges as $charge) {
                        if ($weight > $charge->weight) {
                            $applicableCharge = $charge;
                        } else {
                            break;
                        }
                    }
                    if ($applicableCharge) {
                        $quotient = $quantity / 2;
                        $remainder = $quantity % 2;
                        $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                        $total_delivery_charge += $calculated_charge;
                    }
                }
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'delivery_charge' => $total_delivery_charge
                    ],
                    'message' => __('Delivery charges calculated successfully.')
                ];
            } else {
                 $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Showroom not found.')
                ];
            }            
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']); 
        
    }

    //S
    public function deliveryChargeOLD() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $cityId = $this->request->getData('city_id');
            //standard,express
            if($this->request->getData('delivery_mode')){
                $deliveryMode = $this->request->getData('delivery_mode');
            }else {
                $deliveryMode = 'standard';
            }
            
            $weightQuantityArray = $this->request->getData('weightQuantityArray');

            if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {              
                $result = [
                'status' => 'error',
                'code' => 200,
                'message' => __('Invalid weight and quantity data.')
                ];
                goto label;
            }

            $deliveryCharges = $this->DeliveryCharges->find()
            ->where([
                'city_id' => $cityId,
                'delivery_mode' => $deliveryMode,
                'status' => 'A'
            ])
            ->order(['weight' => 'ASC'])
            ->toArray();

            $total_delivery_charge = 0.00;

            if (empty($deliveryCharges)) {                    
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'delivery_charge' => $total_delivery_charge
                    ],
                    'message' => __('No delivery charges found for the selected criteria.')
                    ];
                goto label;
            }
            
            foreach ($weightQuantityArray as $item) {
                $weight = $item['weight'];
                $quantity = $item['quantity'];
                $applicableCharge = null;

                foreach ($deliveryCharges as $charge) {
                    if ($weight > $charge->weight) {
                        $applicableCharge = $charge;
                    } else {
                        break;
                    }
                }
                if ($applicableCharge) {
                    $quotient = $quantity / 2;
                    $remainder = $quantity % 2;
                    $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                    $total_delivery_charge += $calculated_charge;
                }
            }           
            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    'delivery_charge' => $total_delivery_charge
                ],
                'message' => __('Delivery charges calculated successfully.')
            ];
                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    //deliver charge new
    public function deliveryCharge() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $cityId = $this->request->getData('city_id');
            //standard,express
            if($this->request->getData('delivery_mode')){
                $deliveryMode = $this->request->getData('delivery_mode');
            }else {
                $deliveryMode = 'standard';
            }
            
            $weightQuantityArray = $this->request->getData('weightQuantityArray');
            $sizeQuantityArray = $this->request->getData('sizeQuantityArray');

            $charge_res = $this->DeliveryCharges->calculateDeliveryCharge($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray);

            if($charge_res['status'] == 'success'){

                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'data' => [
                        'delivery_charge' => $charge_res['total_delivery_charge']
                    ],
                    'message' => __('Delivery charges calculated successfully.')
                ];

            } elseif($charge_res['status'] == 'error') {

                $result = [
                'status' => 'error',
                'code' => 200,
                'message' => $charge_res['message']
                ];
                goto label;
            } 
                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    //S generate Order No
    public function generateOrderNo() {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $order_no = $this->Orders->generateUniqueOrderNum();
            $order_num = 'MA-'.$order_no;             
            if ($order_no) {
                $result = ['status' => 'success', 'data' => ['order_num' => $order_num]];
                $this->response = $this->response->withStatus(200);                            
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to generate order no.')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    //S support desk
    public function listSupportTickets() {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                if($roleName == 'Showroom Supervisor'){
                    $cond = ['Showrooms.showroom_supervisor'=>$userId];
                    $showroom = $this->Showrooms->find() ->select(['id'])->where([$cond])->all()->extract('id')->toList(); // Convert to an array
                } else if($roleName == 'Showroom Manager') {
                    $cond = ['Showrooms.showroom_manager'=>$userId];
                    $showroom = $this->Showrooms->find() ->select(['id'])->where([$cond])->all()->extract('id')->toList(); // Convert to an array
                } else if($roleName == 'Sales Person'){
                    $showroom = $this->Showrooms->find()
                        ->select(['Showrooms.id'])
                        ->innerJoinWith('ShowroomUsers')
                        ->where(['ShowroomUsers.user_id' => $userId])
                        ->all()->extract('id')->toList(); // Convert to an array
                }  
            } 

            $data = $this->request->getQuery();
            
            if($data['page']){
                $page = (int)$data['page'];
            }else{
                $page = 1;
            }
            if($data['limit']){
                $limit = (int)$data['limit'];
            }else{
                $limit = 10;
            }

            $filter_ticket_status = $data['status'];
            $filter_support_category = $data['support_category_id'];
            //issue name/ticket ID
            $search_str = $data['search_str'];

            //$unread_tickets = $this->SupportTickets->unreadSupportTickets();
            //$unread_count = count($unread_tickets);
            if($roleName == 'Showroom Supervisor') {
                $open_count = $this->SupportTickets->find()
                            ->where(['status' => 'Open', 'showroom_id IN' => $showroom])
                            ->count();
            } else {
                $open_count = $this->SupportTickets->find()
                            ->where(['status' => 'Open', 'created_by' => $userId])
                            ->count();
            }
            
            $tickets = $this->SupportTickets->listSupportTickets($userId, $showroom, $roleName, $filter_ticket_status, $filter_support_category, $search_str, $page, $limit);         
            
            if ($tickets) {
                $result = ['status' => __('success'), 'data' => [/*'unread_count'=>$unread_count, 'unread_tickets'=>$unread_tickets, */ 'open_count'=>$open_count, 'tickets' => $tickets]];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S support desk
    public function viewSupportTicket() {

        if ($this->request->is('get')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            } 

            $data = $this->request->getQuery();
            $support_ticket_id = $data['id'];

            //mark is read 1
            /*$ticket_entity = $this->SupportTickets->get($support_ticket_id);        
            $isread['is_read'] = 1;
            $ticket_entity = $this->SupportTickets->patchEntity($ticket_entity, $isread);
            $res = $this->SupportTickets->save($ticket_entity);*/

            $detail = $this->SupportTickets->viewTicketDetail($support_ticket_id);         
            
            if ($detail) {
                $result = ['status' => __('success'), 'data' => $detail];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => (object)[], 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);         
    }

    //S support desk
    public function replySupportTicket() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData();
            $support_ticket_id = $data['id'];           
            $data['updated_by'] = $userId;              

            $addUpdate = $this->SupportTicketUpdates->addUpdate($support_ticket_id, $data); 
            
            if($addUpdate){
                $ticket_update_id = $addUpdate;
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_update_id', $ticket_update_id) : [];
            }
            
            if ($addUpdate) {
                $result = ['status' => 'success', 'message' => __('Comment added successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to add comment. Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);    
    }

    //S support desk
    public function addSupportTicket() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                if($roleName == 'Showroom Supervisor'){
                    $cond = ['Showrooms.showroom_supervisor'=>$userId];
                    $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                } else if($roleName == 'Showroom Manager') {
                    $cond = ['Showrooms.showroom_manager'=>$userId];
                    $showroom = $this->Showrooms->find()->select(['id','name'])->where(['status'=>'A', $cond])->order(['name'=>'ASC'])->limit(1)->first();
                } else if($roleName == 'Sales Person'){
                    $showroom = $this->Showrooms->find()
                        ->select(['Showrooms.id', 'Showrooms.name'])
                        ->innerJoinWith('ShowroomUsers')
                        ->where(['ShowroomUsers.user_id' => $userId])
                        ->order(['Showrooms.name' => 'ASC'])
                        ->limit(1)
                        ->first(); // Fetches only the first result
                }                
                $showroom_id = $showroom['id'];
            }

            $data = $this->request->getData();
            $data['created_by'] = $userId;
            $data['updated_by'] = $userId;          
            $data['showroom_id'] = $showroom_id; 
            
            $timestamp = FrozenTime::now()->format('YmdHis'); // Current date and time in YYYYMMDDHHMMSS format
            $randomNumber = mt_rand(1000, 9999); // Random 4-digit number
            $data['ticketID'] = "$timestamp-$randomNumber";   

            $addTicket = $this->SupportTickets->addTicket($data);
            $ticket_id = $addTicket;
            /*if($addTicket){
                $ticket_id = $addTicket;
                $addUpdate = $this->SupportTicketUpdates->addUpdate($ticket_id, $data); 
            }*/
            if($addTicket){
                $uploadImages = !empty($data['images']) ? $this->supportImageUpload('ticket_id', $ticket_id) : [];
            }
            
            if ($addTicket) {
                $result = ['status' => 'success', 'message' => __('Ticket created successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to create ticket. Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S
    private function supportImageUpload($flag, $id)
    {
        $files = $this->request->getData('images');        
        $uploadedImages = [];
        $i = 0;
        foreach ($files as $file) {
            if ($file->getError() === UPLOAD_ERR_OK) {
                $fileName = trim($file->getClientFilename());
                if (!empty($fileName)) {
                    $imageTmpName = $file->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Settings.SUPPORT_DESK');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                    $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                    if ($uploadResult !== 'Success') {
                        $this->Flash->error(__('Image ' . $fileName . ' could not be uploaded. Please, try again.'));
                    } else {
                        $supportImage = $this->SupportTicketImages->newEmptyEntity();                        
                        if($flag == 'ticket_update_id'){
                            $supportImage->support_ticket_update_id = $id;
                        }else{
                            $supportImage->support_ticket_id = $id;
                        }
                        $supportImage->image = $folderPath . $imageFile;
                        $i++;
                        if ($this->SupportTicketImages->save($supportImage)) {
                            $uploadedImages[] = $folderPath . $imageFile; // Collecting the image paths
                        } else {
                            $this->Flash->error(__('Image ' . $fileName . ' could not be saved. Please, try again.'));
                        }
                    }
                }
            }
        }
        return $uploadedImages;
    }

    //S support desk
    public function updateTicketStatus() {

        if ($this->request->is('post')) {

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }

            $data = $this->request->getData();
            $support_ticket_id = $data['id'];           
            $data['status'] = 'Closed';             

            $changeStatus = $this->SupportTickets->changeStatus($support_ticket_id, $data);
            
            if ($changeStatus) {
                $result = ['status' => 'success', 'message' => __('Status changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => 'error', 'message' => __('Unable to change status. Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S support desk
    public function listSupportCategories() {
        
       if ($this->request->is('get')) { 
            
            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {
                 $roleId  = $identity->get('role_id');
                 $userId = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;
            }           

            $listCategory = $this->SupportCategories->getAllSupportCategories();
            
            if ($listCategory) {
                $result = ['status' => __('success'), 'data' => $listCategory];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $listCategory, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);        
    }

    //S list purchase order
    public function listPurchaseOrder(){

    }

    //S view purchase request
    public function viewPurchaseRequest(){
        
    }

    //S new purchase request
    public function addPurchaseRequest(){
        
    }

    //S edit purchase request
    public function editPurchaseRequest(){
        
    }

    public function loyaltyPoints()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
                $data = $this->request->getData();
                $customerId = $data['customer_id'];
                $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);

                if ($loyaltyDetails['status'] === 'error') {
                    $result = ['status' => __('error'), 'message' => $loyaltyDetails['message']];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => __('success'), 'data' => $loyaltyDetails['data'], 'message' => $loyaltyDetails['message']];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function viewProfile()
    {
        if ($this->request->is('get')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
               
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                $user_data = $this->Users->find()                    
                    ->where(['Users.id' => $userId])
                    ->first();

                if ($user_data) {

                    if($user_data['profile_pic']){
                       $user_data['profile_pic'] = $this->Media->getCloudFrontURL($user_data['profile_pic']); 
                    }
                    $result = [
                        'status' => 'success',
                        'data' => $user_data
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'error', 'message' => __('User not found')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function editProfile()
    {
        $result = [
            'status' => 'error',
            'code' => 200,
            'message' => __('An unexpected error occurred.')
        ];
        $this->response = $this->response->withStatus(200);

        if ($this->request->is(['post', 'put'])) {
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('User is not authenticated')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            } else {              

                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                $user_data = $this->Users->find()                    
                    ->where(['Users.id' => $userId])
                    ->first();

                $data = $this->request->getData();
                if ($user_data) {
                   
                    $first_name = $data['first_name'];
                    $last_name  = $data['last_name'];

                    $user_attributes = [
                        'first_name' => $first_name,
                        'last_name' => $last_name,
                        'country_code' => $data['country_code'],
                        'mobile_no' => $data['mobile_no']
                    ];                

                    // Check if the new mobile number is already used by another user
                    $existingMobile = $this->Users->find()
                        ->where(['mobile_no' => $data['mobile_no'], 'id !=' => $userId, 'status' => 'A'])
                        ->first();

                    if ($existingMobile) {
                        $result = [
                            'status' => __('error'),
                            'code' => 200,
                            'message' => __('This mobile number is already in use by another user.')
                        ];
                        $this->response = $this->response->withStatus(200);
                        goto label;
                    }

                    if (isset($data['profile_pic']) && $data['profile_pic']->getError() === UPLOAD_ERR_OK) {
                        
                        $profile_pic = $data['profile_pic'];
                        $fileName = trim($profile_pic->getClientFilename());

                        if (!empty($fileName)) {
                            $imageTmpName = $profile_pic->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $userFolder = Configure::read('Settings.USER');
                            
                            $folderPath = $uploadFolder.$userFolder.'profile_photo/';
                            $targetdir = WWW_ROOT . $folderPath;
                            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                            if ($uploadResult !== 'Success') {
                                $result = [
                                    'status' => __('error'),
                                    'code' => 200,
                                    'message' => __('Profile picture could not be uploaded. Please, try again.')
                                ];
                                $this->response = $this->response->withStatus(200);
                                goto label;
                            } else {
                                $user_attributes['profile_pic'] = $folderPath . $imageFile;
                            }
                        }
                    } else {
                        $user_attributes['profile_pic'] = $user_data['profile_pic'];
                    }

                    $user = $this->Users->update_user_by_id($userId, $user_attributes);

                    if ($user) {

                        $user['profile_pic'] = $this->Media->getCloudFrontURL($user['profile_pic']);

                        if ($user) {
                            $result = [
                                'status' => 'success',
                                'code' => 200,
                                'data' => $user,
                                'message' => __('Profile updated successfully')
                            ];
                            $this->response = $this->response->withStatus(200);
                        } else {
                            $result = [
                                'status' => 'error',
                                'code' => 200,
                                'message' => __('Failed to update profile')
                            ];
                            $this->response = $this->response->withStatus(200);
                        }
                    } else {
                        $result = [
                            'status' => 'error',
                            'code' => 200,
                            'message' => __('Failed to update user information'),
                            // 'errors' => $user->getErrors()
                        ];
                        $this->response = $this->response->withStatus(200);
                    }
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Driver not found')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function listNotifications()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(200);
            } else {
               
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');

                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;

                $data = $this->request->getData();
                //flag 0:unread, 1:read 2:all
                $flag = $data['flag'];
                if (empty($flag) && $flag !== "0") {
                    $flag = 2;
                }
                //$notification_count = $this->Notifications->notificationCount ($userId);
                $notifications = $this->Notifications->userNotifications ($userId, $flag);
                
                if ($notifications) {                    
                    $result = [
                        'status' => 'success',
                        'data' => $notifications
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = ['status' => 'success', 'data' => $notifications, 'message' => __('Notification not found')];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S cash desk
    public function cashDesk(){

        if ($this->request->is('post')) {  

            // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {                
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;             
            } 

            $roles = $this->Roles->find()
                ->where([
                    'name IN' => ['Showroom Manager', 'Sales Person'/*, 'Showroom Supervisor', 'Admin', 'Warehouse Manager', 'Warehouse Assistant'*/],
                    'status' => 'A'
                ])
                ->all()
                ->toArray();

            $all_roles = array_column($roles, 'id'); // Extracts role IDs

            $cash_handovers = []; $sales_data = []; $today_sales = 0;  $total_paid = 0; $handoverCash = 0; $total_cash = 0;

            $data = $this->request->getData();
            $showroom_id = $data['showroom_id'];

            if($roleName == 'Showroom Manager'){

                //sales order
                //$sales_data = $this->Orders->salesOrder($showroom_id, $all_roles);
                $sales_data = $this->Orders->salesPersonOrder($showroom_id);
                //$today_sales = $this->Orders->getCurrentDaySales($showroomId);
                $manager_sales = $this->Orders->getManagerSales($showroom_id, $userId);
                
                foreach ($sales_data as $key1 => $value1) {
                    $today_sales = $today_sales + $value1['total_amount'];
                }
                //$today_sales = number_format($today_sales ?? 0, 2, '.', '');

                $total_cash = $manager_sales + $today_sales;               
                $total_cash = number_format($total_cash, 2, '.', '');

            } else if ($roleName == 'Showroom Supervisor') {

                //cash handovers
                $cash_handovers = $this->CashHandovers->cashHandoverDetail($userId, $showroom_id);
                $today_cash = 0;
                foreach ($cash_handovers as $key3 => $value3) {
                    $today_cash = $today_cash + $value3['amount'];
                }

                $total_cash = number_format($today_cash, 2, '.', '');
            }            

            //supplier payments
            //$supplier_payment_data = $this->SupplierPurchaseOrders->getSupplierOrders($showroom_id);           

            /*$total_pending = 0;
            foreach ($supplier_payment_data as $key2 => $value2) {
                if($value2['payment_status'] == 'Pending') {
                    $total_pending =  $total_pending + $value2['total_amount'];
                }
            } 
            $total_pending = number_format($total_pending ?? 0, 2, '.', ''); // Format to 2 decimal places*/

            $supplier_payment_data = $this->SupplierPayment->getSupplierPayments($showroom_id); 
            
            if($supplier_payment_data){
                foreach ($supplier_payment_data as $key2 => $value2) {                
                    $total_paid =  $total_paid + $value2['amount'];              
                } 
            }
            $total_paid = number_format($total_paid ?? 0, 2, '.', ''); // Format to 2 decimal places

             

            if($roleName == 'Showroom Manager'){
                //manager id
                $handoverCash = $this->CashHandovers->handoverByManager($userId);

                $cash_desk_close_amount = $total_cash - $total_paid - $handoverCash;

                $available_cash = $total_cash - $total_paid - $handoverCash;  
            } else{

                $todayClosure = $this->CashDeskClosures->find()
                    ->where([
                        'showroom_id' => $showroom_id,
                        'closed_by' => $userId,
                        'DATE(closing_time)' => date('Y-m-d')
                    ])
                    ->order(['closing_time' => 'DESC'])
                    ->first();

                if ($todayClosure) {
                    $available_cash = $total_cash - $total_paid - $todayClosure->total_cash;
                    // if fully closed, remainingCash = 0
                } else {
                    $available_cash = $total_cash - $total_paid; // no closing yet for today
                }
            }

            $cash_desk_close = $this->CashDeskClosures->find()->select(['closing_time'])
            ->where([
                'showroom_id' => $showroom_id,
                'closed_by' => $userId
            ])->order(['closing_time' => 'DESC'])->first()->toArray();
            $last_cash_desk_close = $cash_desk_close['closing_time'];

            $result = [
                    'status' => 'success',
                    'data' => ['cash_handovers'=>$cash_handovers, 'sales_data'=>$sales_data, 'supplier_payment_data'=>$supplier_payment_data, 'today_sales'=>$today_sales, 'total_paid'=>$total_paid, 'available_cash'=>$available_cash, 'cash_desk_close_amount'=>$cash_desk_close_amount, 'last_cash_desk_close'=>$last_cash_desk_close]
                ];
            $this->response = $this->response->withStatus(200);
                                 
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S cash desk
    public function cashDeskClose(){

        if ($this->request->is('post')) {  

           // Get the user info from the logged-in user's identity
            $identity = $this->request->getAttribute('identity');
            if($identity) {                
                $roleId  = $identity->get('role_id');
                $userId  = $identity->get('id');
                // Load the role name based on the role_id
                $role = $this->Users->Roles->get($roleId);
                $roleName = $role->name;             
            } 

            $data = $this->request->getData();
            $data['closed_by'] = $userId;

            // Get Opening Balance from previous day’s closing
            $previousClosure = $this->CashDeskClosures->find()
                ->where([
                    'showroom_id' => $data['showroom_id'],
                    'closed_by' => $userId,
                    'DATE(closing_time) <' => date('Y-m-d')
                ])
                ->order(['closing_time' => 'DESC'])
                ->first();

            $opening_balance = $previousClosure ? $previousClosure->closing_balance : 0;
            $data['opening_balance'] = $opening_balance;

            // Calculate closing balance
            $data['closing_balance'] = $opening_balance + $data['available_cash'] - $data['total_cash'];
             

             /*
             * Field/Concept Explanation:
             *
             * available_cash  : (sent to API) 
             *   - The total cash manager has at the end of the day
             *   - Calculated as sales - payments - previous handovers
             *   - This is the expected amount to be handed over
             *
             * total_cash : (input from manager at closing)
             *   - The actual amount manager hands over to supervisor
             *   - Can be full or partial payment of available_cash
             *
             * Difference (available_cash - total_cash):
             *   - Remaining unpaid cash amount
             *   - Carried forward to next day’s opening balance
             */

            // Mark status based on whether it was fully paid
            if ($data['total_cash'] == $data['available_cash']) {
                $data['status'] = 'closed';
            } else {
                $data['status'] = 'open'; // not fully closed, balance carried forward
            }

            $showroom = $this->Showrooms->showroomDetailById($data['showroom_id']);

            if($roleName == 'Showroom Manager'){

                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data); 

                if($data['total_cash']){
                    $cash_handover = $this->CashHandovers->newEmptyEntity();
                    $cash_handover['showroom_manager'] = $userId;
                    $cash_handover['handed_to_supervisor'] = $showroom['showroom_supervisor'];
                    $cash_handover['amount'] = $data['total_cash'];
                    $cash_handover['handover_date'] = date('Y-m-d H:i:s');
                    $cash_handover['showroom_id'] = $data['showroom_id'];
                    //$cash_handover['remarks'] = $data['remarks'];
                    $save_cash_handover = $this->CashHandovers->save($cash_handover); 
                }

            } else if ($roleName == 'Showroom Supervisor') {

                $cash_desk_close = $this->CashDeskClosures->cashDeskClose($data); 
            }           
                        
            if ($cash_desk_close) {
                $result = ['status' => __('success'), 'message' => __('Cash desk closed')];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('error'), 'message' => __('Unable to close, Please try again.')];
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        label:
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function contactUsQueryTypes()
    {
        if ($this->request->is('get')) {

            $categoriesQuery = $this->ContactQueryTypes->getAllQueryTypes();
            $categoriesArray = $categoriesQuery->toArray();

            if (empty($categoriesArray)) {
                $result = [
                    'status' => 'error',
                    'message' => __('No query type found')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => $categoriesArray
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function contactUs()
    {
        if ($this->request->is('post')) {

            $data = $this->request->getData();

            if (empty($data['name'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Name is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['phone_number'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Phone number is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['email_id']) || !filter_var($data['email_id'], FILTER_VALIDATE_EMAIL)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Valid email ID is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['query_type'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Query type is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            if (empty($data['message'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Message is required.')
                ];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $queryType = $this->ContactQueryTypes->find()
                ->select(['name'])
                ->where(['id' => $data['query_type']])
                ->first();

            $type = $queryType ? $queryType->name : __('No Query Type provided');

            $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
            $to = $adminEmails[0];
            $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
            $subject = 'Contact Us Form Submission';
            $template = 'contact_us';

            $viewVars = [
                'name' => $data['name'],
                'phone' => $data['phone_number'],
                'email' => $data['email_id'],
                'query_type' => $type,
                'message' => $data['message'],
            ];

            $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);

            if ($sendEmail) {
                $result = [
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Your message has been sent successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to send the email. Please try again later.')
                ];
            }

            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //S
    public function changePassword()
    {
        if ($this->request->is('post')) {

            $data = $this->request->getData();
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                $result = ['status' => 'error', 'message' => __('User is not authenticated')];
                $this->response = $this->response->withStatus(401);
                goto label;
            }

            $userId = $identity->get('id');

            if ($data['new_password'] !== $data['confirm_password']) {
                $result = ['status' => 'error', 'message' => __('New password and confirm password do not match')];
                $this->response = $this->response->withStatus(200);
                goto label;
            }

            $changePasswordResult = $this->Users->changeUserPassword($userId, $data['old_password'], $data['new_password']);

            if ($changePasswordResult['status'] === 'success') {
                $result = ['status' => __('success'), 'message' => __('Password changed successfully.')];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = ['status' => 'error', 'message' => __('Old password is incorrect.')];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        label:
        $this->set(compact('result'));
        $this->viewBuilder()->setOption('serialize', ['result']);
    }


    //ZAID
    public function getWarehouses()
    {
        if ($this->request->is('get')) {            

            $warehouses = $this->Warehouses->allWarehouses();
            if ($warehouses) {
                $result = ['status' => __('success'), 'data' => $warehouses];
                $this->response = $this->response->withStatus(200);
            } else {            
                $result = ['status' => __('success'), 'data' => $warehouses, 'message' => __('No data found')];
                $this->response = $this->response->withStatus(200);
            }                       
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }
        
        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getStockList()
    {
        $this->request->allowMethod(['get']); // Allow only GET requests

        // Get the logged-in user details
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Unauthorized access'
            ]));
        }

        // Extract user details
        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Fetch the user's role
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);
        $conditions = [];

        // Apply filters based on role
        if ($roleName === 'showroom manager') {
            $managerShowroom = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_manager' => $userId])
                ->first();
            
            if ($managerShowroom) {
                $conditions['ProductStocks.showroom_id'] = $managerShowroom->id;
            }
        } elseif ($roleName === 'warehouse manager') {
            $warehouse = $this->Warehouses->find()
                ->select(['id'])
                ->where(['manager_id' => $userId])
                ->first();

            if ($warehouse) {
                $conditions['ProductStocks.warehouse_id'] = $warehouse->id;
            }
        } elseif ($roleName === 'showroom supervisor') {
            $supervisorShowrooms = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_supervisor' => $userId])
                ->all()
                ->extract('id')
                ->toList();

            if (!empty($supervisorShowrooms)) {
                $conditions['ProductStocks.showroom_id IN'] = $supervisorShowrooms;
            }
        }

        // Fetch product stock details with associations
        $productStocks = $this->ProductStocks->find()
            ->select([
                'ProductStocks.id',
                'ProductStocks.quantity',
                'ProductStocks.product_attribute_id',
                'ProductStocks.product_variant_id',
                'ShowroomWarehouse' => $this->ProductStocks->find()->func()->coalesce(['Showrooms.name', 'Warehouses.name']),
                'Products.id',
                'Products.name',
                'Products.reference_name',
                'Products.sku',
                'ProductVariants.variant_name',
                'ProductVariants.sku'
            ])
            ->contain([
                'Showrooms' => ['fields' => ['Showrooms.id', 'Showrooms.name']],
                'Warehouses' => ['fields' => ['Warehouses.id', 'Warehouses.name']],
                'Products' => ['fields' => ['Products.id', 'Products.name', 'Products.sku']],
                'ProductVariants' => ['fields' => ['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']]
            ])
            ->where($conditions)
            ->order(['ProductStocks.id' => 'DESC'])
            ->toArray();

        // Enhance stock data with additional details
        foreach ($productStocks as &$stock) {
            // Initialize an attributes array in each item
            $stock->attributes = [];

            if ($stock->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $stock->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                if ($attributes) {
                    $stock->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }

            // Initialize supplier price
            $stock->supplier_price = 0;

            // Check if product_variant_id is available and query supplier_products accordingly
            if ($stock->product_variant_id) {
                $supplierProduct = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_variant_id' => $stock->product_variant_id])
                    ->first();

                if ($supplierProduct) {
                    $stock->supplier_price = $supplierProduct->supplier_price;
                } else {
                    // If not found for product_variant_id, fallback to product_id
                    $supplierProductFallback = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where(['product_id' => $stock->product->id])
                        ->first();
                    
                    $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
                }
            } else {
                // If no product_variant_id, fallback to the product's supplier_price
                $supplierProductFallback = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where(['product_id' => $stock->product->id])
                    ->first();
                
                $stock->supplier_price = $supplierProductFallback ? $supplierProductFallback->supplier_price : 0;
            }

            // Calculate the value (quantity * supplier_price)
            $stock->value = $stock->supplier_price * $stock->quantity;

            // Fetch minimum product quantity for the associated category
            $productId = $stock->product->id ?? null;
            if ($productId) {
                $categoryData = $this->Products->find()
                    ->select(['Categories.min_product_quantity'])
                    ->leftJoinWith('ProductCategories.Categories')
                    ->where(['Products.id' => $productId])
                    ->first();

                $stock->min_product_quantity = $categoryData['_matchingData']['Categories']['min_product_quantity'] ?? 'N/A';
            } else {
                $stock->min_product_quantity = 'N/A';
            }
        }

        // Response formatting
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $productStocks
        ]));
    }

    //ZAID
    public function getShowroomStocks()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['showroom_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Showroom ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $showroomId = $data['showroom_id'];

                // Calling the method directly
                $productStocks = $this->Showrooms->showroomStocks($showroomId)->all();

                if (!$productStocks->isEmpty()) {
                    $result = [
                        'status' => __('success'),
                        'data' => $productStocks->toArray()
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => __('success'),
                        'data' => [],
                        'message' => __('No stock data found for the given showroom')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getWarehouseStocks()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['warehouse_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Warehouse ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $warehouseId = $data['warehouse_id'];

                // Calling the method directly
                $warehouseStocks = $this->Warehouses->warehouseStocks($warehouseId)->all();

                if (!$warehouseStocks->isEmpty()) {
                    $result = [
                        'status' => __('success'),
                        'data' => $warehouseStocks->toArray()
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => __('success'),
                        'data' => [],
                        'message' => __('No stock data found for the given warehouse')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getShowroomStockRequests()
    {
        $this->request->allowMethod(['get']); // Allow only GET requests

        // Get the logged-in user details
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Extract user details
        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Fetch the user's role
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);
        $conditions = [];

        // Role-based filtering
        if ($roleName === 'showroom manager') {
            $managerShowroom = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_manager' => $userId])
                ->first();

            if ($managerShowroom) {
                $conditions['OR'] = [
                    'StockRequests.to_showroomID' => $managerShowroom->id,
                    'StockRequests.showroom_id' => $managerShowroom->id
                ];
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No stock requests found for this showroom manager'
                ]));
            }
        } elseif ($roleName === 'showroom supervisor') {
            $supervisorShowrooms = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_supervisor' => $userId])
                ->all()
                ->extract('id')
                ->toList();

            if (!empty($supervisorShowrooms)) {
                $conditions['OR'] = [
                    'StockRequests.to_showroomID IN' => $supervisorShowrooms,
                    'StockRequests.showroom_id IN' => $supervisorShowrooms
                ];
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No stock requests found for this showroom supervisor'
                ]));
            }
        } elseif ($roleName === 'warehouse manager') {
            $warehouse = $this->Warehouses->find()
                ->select(['id'])
                ->where(['manager_id' => $userId])
                ->first();

            if ($warehouse) {
                $conditions['StockRequests.requestor_type'] = 'Showroom';
                $conditions['StockRequests.warehouse_id'] = $warehouse->id;
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No stock requests found for this warehouse manager'
                ]));
            }
        } else {
            // Admin access: Fetch all showroom stock requests
            $conditions['StockRequests.requestor_type'] = 'Showroom';
        }

        // Common conditions for all roles
        $conditions['StockRequests.status IN'] = ['A', 'I'];

        // Fetch stock requests with associated data
        $stockRequestsQuery = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.warehouse_id',
                'StockRequests.to_showroomID',
                'StockRequests.showroom_id',
                'StockRequests.status',
                'Warehouses.name',
                'Showrooms.name',
                'ToShowroom.name',
                'total_items' => $this->StockRequests->StockRequestItems->find()
                    ->func()
                    ->sum(
                        '(CASE 
                            WHEN StockRequests.request_status IN ("Approved", "Completed") 
                                THEN StockRequestItems.fulfilled_quantity 
                            ELSE StockRequestItems.requested_quantity 
                        END)'
                    ),
                'total_value' => $this->StockRequests->StockRequestItems->find()
                    ->func()
                    ->sum(
                        '(CASE 
                            WHEN StockRequests.request_status IN ("Approved", "Completed") AND StockRequestItems.product_variant_id IS NULL 
                                THEN Products.promotion_price * StockRequestItems.fulfilled_quantity 
                            WHEN StockRequests.request_status IN ("Approved", "Completed") AND StockRequestItems.product_variant_id IS NOT NULL 
                                THEN ProductVariants.promotion_price * StockRequestItems.fulfilled_quantity 
                            WHEN StockRequestItems.product_variant_id IS NULL 
                                THEN Products.promotion_price * StockRequestItems.requested_quantity 
                            ELSE ProductVariants.promotion_price * StockRequestItems.requested_quantity 
                          END)'
                    )
            ])
            ->contain([
                'Warehouses',
                'Showrooms'
            ])
            ->leftJoinWith('StockRequestItems')
            ->leftJoinWith('StockRequestItems.Products')
            ->leftJoinWith('StockRequestItems.ProductVariants')
            ->leftJoin(
                ['ToShowroom' => 'showrooms'],
                ['ToShowroom.id = StockRequests.to_showroomID']
            )
            ->where($conditions)
            ->group(['StockRequests.id'])
            ->order(['StockRequests.request_date' => 'DESC'])
            ->toArray();

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        // Format total_value
        foreach ($stockRequestsQuery as &$request) {
            if (!empty($request->total_value)) {
                $request->total_value = number_format(
                    (float)$request->total_value, 
                    2, 
                    $decimalSeparator, 
                    $thousandSeparator
                ) . ' ' . $currencySymbol;
            } else {
                $request->total_value = number_format(0, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol;
            }
        }

        // Response formatting
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $stockRequestsQuery
        ]));
    }

    //ZAID
    public function viewStockRequest()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['request_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Request ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $requestId = $data['request_id'];

                // Fetch Stock Request Details
                $stockRequest = $this->StockRequests->find()
                    ->select([
                        'StockRequests.id',
                        'StockRequests.request_date',
                        'StockRequests.request_status',
                        'StockRequests.requested_by',
                        'StockRequests.to_showroomID',
                        'StockRequests.manager_review_status',
                        'StockRequests.manager_reviewed_time',
                        'StockRequests.reviewed_by',
                        'StockRequests.supervisor_verify_status',
                        'StockRequests.supervisor_verified_time',
                        'StockRequests.verified_by',
                        'StockRequests.status',
                        'Warehouses.name',
                        'Showrooms.name',
                        'Users.first_name',
                        'Users.last_name',
                        'ToShowroom.name',
                        'ReviewedUser.first_name',
                        'ReviewedUser.last_name',
                        'VerifiedUser.first_name',
                        'VerifiedUser.last_name',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'DriverUser.first_name',
                        'DriverUser.last_name'
                    ])
                    ->contain([
                        'Warehouses',
                        'Showrooms',
                        'Users'
                    ])
                    ->leftJoin(
                        ['ToShowroom' => 'showrooms'],
                        ['ToShowroom.id = StockRequests.to_showroomID']
                    )
                    ->leftJoin(
                        ['ReviewedUser' => 'users'],
                        ['ReviewedUser.id = StockRequests.reviewed_by']
                    )
                    ->leftJoin(
                        ['VerifiedUser' => 'users'],
                        ['VerifiedUser.id = StockRequests.verified_by']
                    )
                    ->leftJoin(
                        ['StockMovements' => 'stock_movements'],
                        [
                            'StockMovements.referenceID = StockRequests.id',
                            'StockMovements.movement_type' => 'Incoming'
                        ]
                    )
                    ->leftJoin(
                        ['Drivers' => 'drivers'],
                        ['Drivers.id = StockMovements.driver_id']
                    )
                    ->leftJoin(
                        ['DriverUser' => 'users'],
                        ['DriverUser.id = Drivers.user_id']
                    )
                    ->where(['StockRequests.id' => $requestId])
                    ->first();

                $driver_name = $this->StockRequests->find()
                    ->select([
                        'DriverUser.first_name',
                        'DriverUser.last_name'
                    ])
                    ->leftJoin(
                        ['StockMovements' => 'stock_movements'],
                        [
                            'StockMovements.referenceID = StockRequests.id',
                            'StockMovements.movement_type' => 'Outgoing' // Updated filter
                        ]
                    )
                    ->leftJoin(
                        ['Drivers' => 'drivers'],
                        ['Drivers.id = StockMovements.driver_id']
                    )
                    ->leftJoin(
                        ['DriverUser' => 'users'],
                        ['DriverUser.id = Drivers.user_id']
                    )
                    ->where(['StockRequests.id' => $requestId])
                    ->first();

                if (!$stockRequest) {
                    $result = [
                        'status' => __('error'),
                        'code' => 404,
                        'message' => __('Stock Request not found')
                    ];
                    $this->response = $this->response->withStatus(404);
                } else {
                    // Fetch Stock Request Items
                    $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                        ->select([
                            'StockRequestItems.id',
                            'StockRequestItems.requested_quantity',
                            'StockRequestItems.product_id',
                            'StockRequestItems.product_variant_id',
                            'StockRequestItems.product_attribute_id',
                            'StockRequestItems.stock_request_id',
                            'StockRequestItems.manager_approved_quantity',
                            'StockRequestItems.supervisor_approved_quantity',
                            'StockRequestItems.fulfilled_quantity',
                            'StockRequestItems.status',
                            'Products.id',
                            'Products.name',
                            'Products.promotion_price',
                            'Products.sku',
                            'ProductVariants.id',
                            'ProductVariants.variant_name',
                            'ProductVariants.promotion_price',
                            'ProductVariants.sku',
                        ])
                        ->leftJoinWith('Products')
                        ->leftJoinWith('ProductVariants')
                        ->where(['StockRequestItems.stock_request_id' => $requestId])
                        ->toArray();

                    foreach ($stockRequestItems as &$item) {
                        $item->attributes = [];

                        if ($item->product_attribute_id) {
                            $attributes = $this->ProductAttributes->find()
                                ->where(['ProductAttributes.id' => $item->product_attribute_id])
                                ->contain([
                                    'Attributes' => ['fields' => ['Attributes.name']],
                                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                                ])
                                ->first();

                            if ($attributes) {
                                $item->attributes = [
                                    'attribute_name' => $attributes->attribute->name ?? '',
                                    'attribute_value' => $attributes->attribute_value->value ?? ''
                                ];
                            }
                        }
                    }

                    $currencyConfig = Configure::read('Settings.Currency.format');
                    $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
                    $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
                    $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

                    $result = [
                        'status' => __('success'),
                        'data' => [
                            'stockRequest' => $stockRequest,
                            'driverName' => $driver_name,
                            'stockRequestItems' => $stockRequestItems,
                            'currencySymbol' => $currencySymbol,
                            'decimalSeparator' => $decimalSeparator,
                            'thousandSeparator' => $thousandSeparator
                        ]
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function addStockRequest()
    {
        $this->request->allowMethod(['post']);
        $requestedUser = $this->Authentication->getIdentity();

        if (!$requestedUser) {
            return $this->response->withType('application/json')
                ->withStatus(401)
                ->withStringBody(json_encode(['message' => 'Unauthorized']));
        }

        $data = $this->request->getData();
        $stockRequest = $this->StockRequests->newEmptyEntity();
        $role = $this->Roles->get($requestedUser->role_id);

        // Assign the authenticated user's ID as the requestor
        $data['requested_by'] = $requestedUser->id;

        // Default values for all stock requests
        $data['requestor_type'] = 'Showroom';
        $data['manager_review_status'] = 'Pending';

        if (strtolower($role->name) === 'showroom supervisor' || strtolower($role->name) === 'admin') {
            // Supervisor/Admin auto-approves the request
            $data['supervisor_verify_status'] = 'Approved';
            $data['supervisor_verified_time'] = date('Y-m-d H:i:s');
            $data['verified_by'] = $requestedUser->id;
            $data['request_status'] = 'Approved';
        } else {
            // Showroom Manager submits request for approval
            $data['supervisor_verify_status'] = 'Pending';
            $data['request_status'] = 'Pending';
        }

        $stockRequest = $this->StockRequests->patchEntity($stockRequest, $data);

        if ($this->StockRequests->save($stockRequest)) {
            if ($data['request_status'] === 'Approved') {
                // Save approved stock request items
                $this->saveStockRequestItemsApproved($stockRequest);
                // Notify the receiving showroom's Manager and Supervisor
                $this->sendStockRequestEmail($stockRequest, 'New Request');
            } else {
                // Save pending stock request items
                $this->saveStockRequestItems($stockRequest);
                // Notify the Manager for approval
                $this->sendStockRequestEmailForManager($stockRequest, 'New Request');
            }

            return $this->response->withType('application/json')
                ->withStatus(201)
                ->withStringBody(json_encode([
                    'message' => 'Stock request saved successfully.',
                    'stock_request_id' => $stockRequest->id
                ]));
        }

        return $this->response->withType('application/json')
            ->withStatus(400)
            ->withStringBody(json_encode(['message' => 'Stock request could not be saved. Please try again.']));
    }

    //ZAID
    protected function saveStockRequestItemsApproved($stock_request)
    {
        if (!empty($stock_request->products) && is_array($stock_request->products)) {
            $stock_request_id = $stock_request->id;

            // Delete existing stock request items for this request
            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            $stockItems = [];

            foreach ($stock_request->products as $product) {
                $stockItems[] = $this->StockRequestItems->newEntity([
                    'stock_request_id' => $stock_request_id,
                    'product_id' => $product['product_id'] ?? null,
                    'product_variant_id' => $product['product_variant_id'] ?? null,
                    'product_attribute_id' => $product['product_attribute_id'] ?? null,
                    'requested_quantity' => $product['quantity'] ?? 0,
                    'supervisor_approved_quantity' => $product['quantity'] ?? 0,
                    'fulfilled_quantity' => $product['quantity'] ?? 0,
                    'status' => 'Approved'
                ]);

                // Update product stock reserved_quantity
                $this->updateProductStocks(
                    strtolower($stock_request->requestor_type),
                    $stock_request->to_showroomID ?? null,
                    $stock_request->warehouse_id ?? null,
                    $product['product_id'] ?? null,
                    $product['product_variant_id'] ?? null,
                    $product['product_attribute_id'] ?? null,
                    $product['quantity'] ?? 0
                );
            }

            // Bulk insert all stock request items
            if (!empty($stockItems)) {
                $this->StockRequestItems->saveMany($stockItems);
            }
        }

        return true;
    }

    //ZAID
    private function sendStockRequestEmail($stock_request, $status)
    {
        // Initialize email recipients
        $toEmails = [];

        // Determine request type
        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Request
            $toShowroomId = $stock_request->to_showroomID;
            $fromShowroomId = $stock_request->showroom_id;

            // Fetch showroom names
            $showrooms = $this->Showrooms->find()
                ->where(['id IN' => [$fromShowroomId, $toShowroomId]])
                ->all()
                ->combine('id', 'name')
                ->toArray();

            $fromShowroomName = $showrooms[$fromShowroomId] ?? 'Unknown Showroom';
            $toShowroomName = $showrooms[$toShowroomId] ?? 'Unknown Showroom';

            // Fetch only the Manager of the receiving showroom
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            $managerEmail = $showroom->manager ? $showroom->manager->email : null;

            $toEmails = array_filter([$managerEmail]); // Remove null values

            $toLocation = "Showroom: {$toShowroomName}";
            $greeting = "Dear Manager,";
        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Request
            $fromShowroomId = $stock_request->showroom_id;

            // Fetch showroom name
            $showroom = $this->Showrooms->find()
                ->where(['id' => $fromShowroomId])
                ->first();

            $fromShowroomName = $showroom ? $showroom->name : 'Unknown Showroom';

            // Fetch Warehouse Name and Manager Email
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for stock request ID: " . $stock_request->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stock_request->created ? $stock_request->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => ucfirst($status),
            'from_showroom' => $fromShowroomName,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $formattedDate,
        ];

        $subject = "New Stock Request #{$stock_request->id} from {$fromShowroomName}";

        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'stock_request_notification', // Template name
            $emailData
        );
    }

    //ZAID
    private function sendStockRequestEmailForManager($stock_request, $status)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Originating Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id);

        if (!empty($stock_request->to_showroomID)) {
            // Requesting stock from another showroom
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            $managerEmail = $showroom->manager ? $showroom->manager->email : null;

            if ($managerEmail) {
                $toEmails[] = $managerEmail;
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Requesting stock from a warehouse
            $fromShowroomId = $stock_request->showroom_id;

            $showroom = $this->Showrooms->get($fromShowroomId, [
                'contain' => ['ShowroomSupervisor']
            ]);

            $supervisorEmail = $showroom->supervisor ? $showroom->supervisor->email : null;

            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($supervisorEmail) {
                $toEmails[] = $supervisorEmail; // Supervisor must approve first
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail;
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => ucfirst($status),
            'from_showroom' => $fromShowroom->name, // ✅ Send From Showroom
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Approval Required";

        $this->Global->send_email(
            $toEmails, 
            null, 
            $subject,
            'stock_request_notification', 
            $emailData
        );
    }

    //ZAID
    protected function saveStockRequestItems($stock_request)
    {
        if (!empty($stock_request->products) && is_array($stock_request->products)) {

            $stock_request_id = $stock_request->id;

            $this->StockRequestItems->deleteAll(['stock_request_id' => $stock_request_id]);

            $stockItems = [];

            foreach ($stock_request->products as $product) {
                $stockItems[] = $this->StockRequestItems->newEntity([
                    'stock_request_id' => $stock_request_id,
                    'product_id' => $product['product_id'] ?? null,
                    'product_variant_id' => $product['product_variant_id'] ?? null,
                    'product_attribute_id' => $product['product_attribute_id'] ?? null,
                    'requested_quantity' => $product['quantity'] ?? 0,
                ]);
            }

            // Bulk insert all stock request items
            if (!empty($stockItems)) {
                $this->StockRequestItems->saveMany($stockItems);
            }
        }

        return true;
    }

    //ZAID
    protected function updateProductStocks($requestTo, $showroom_id, $warehouse_id, $product_id, $product_variant_id, $product_attribute_id, $requested_quantity)
    {
        // Initialize conditions with product_id
        $conditions = [
            'product_id' => $product_id,
        ];

        // Add product_variant_id only if it's not null
        if ($product_variant_id) {
            $conditions['product_variant_id'] = $product_variant_id;
        }

        if ($product_attribute_id) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        // Add either showroom_id or warehouse_id condition
        // if ($requestTo === 'showroom' && $showroom_id) {
        //     $conditions['showroom_id'] = $showroom_id;
        // } elseif ($requestTo === 'warehouse' && $warehouse_id) {
        //     $conditions['warehouse_id'] = $warehouse_id;
        // }

        if (!empty($showroom_id)) {
            $conditions['showroom_id'] = $showroom_id;
        } elseif (!empty($warehouse_id)) {
            $conditions['warehouse_id'] = $warehouse_id;
        }

        // Fetch product stock
        $productStock = $this->ProductStocks->find()
            ->where($conditions)
            ->first();

        if ($productStock) {
            // Update reserved quantity
            $productStock->reserved_stock += $requested_quantity;
            $this->ProductStocks->save($productStock);
        }
    }

    //ZAID
    public function approveStockRequest()
    {
        if (!$this->request->is(['post'])) {
            return $this->response->withStatus(405)->withStringBody(__('Invalid request method.'));
        }

        try {
            $data = $this->request->getData();
            $stockRequestId = $data['stock_request_id'] ?? null;

            if (!$stockRequestId) {

                return $this->response->withStatus(400)->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Missing stock request ID.')
                ]));
            }

            // Get the logged-in user
            $loggedInUser = $this->Authentication->getIdentity();
            if (!$loggedInUser) {
                return $this->response->withStatus(401)->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Unauthorized user.')
                ]));
            }

            $stockRequest = $this->StockRequests->get($stockRequestId);
            if (!$stockRequest) {

                return $this->response->withStatus(404)->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Stock request not found.')
                ]));
            }

            // Determine role of the logged-in user
            $role = $this->Roles->find()
                ->where(['id' => $loggedInUser->role_id])
                ->first();

            if (!$role) {

                return $this->response->withStatus(403)->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('User role not found.')
                ]));
            }

            $statuses = []; // collect each item's status

            foreach ($data['items'] as $item) {
                $status = isset($item['status']) && strtolower($item['status']) === 'reject' ? 'Rejected' : 'Approved';
                $statuses[] = $status;
            }

            // ✅ Check if all are Rejected
            $allRejected = true;
            foreach ($statuses as $status) {
                if (strtolower($status) !== 'rejected') {
                    $allRejected = false;
                    break;
                }
            }

            // Set approval fields based on the role
            if (strtolower($role->name) === 'showroom supervisor') {
                $stockRequest->supervisor_verify_status = $allRejected ? 'Rejected' : 'Approved';
                $stockRequest->supervisor_verified_time = date('Y-m-d H:i:s');
                $stockRequest->verified_by = $loggedInUser->id;
                $stockRequest->request_status = $allRejected ? 'Rejected' : 'Approved';
                $stockRequest->edit_lock_by = null;
                $stockRequest->edit_lock_time = null;
            } else {
                $stockRequest->manager_review_status = $allRejected ? 'Rejected' : 'Approved';
                $stockRequest->manager_reviewed_time = date('Y-m-d H:i:s');
                $stockRequest->reviewed_by = $loggedInUser->id;
                $stockRequest->request_status = $allRejected ? 'Rejected' : 'Approved';
                $stockRequest->edit_lock_by = null;
                $stockRequest->edit_lock_time = null;
            }

            if (!$this->StockRequests->save($stockRequest)) {

                return $this->response->withStatus(500)->withType('application/json')->withStringBody(json_encode([
                    'success' => false,
                    'message' => __('Failed to approve stock request.')
                ]));
            }

            // Delete existing stock request items for the given request ID
            $this->StockRequestItems->deleteAll(['stock_request_id' => $stockRequestId]);

            foreach ($data['items'] as $item) {
                $status = isset($item['status']) && $item['status'] === 'Reject' ? 'Rejected' : 'Approved';

                $stockRequestItem = $this->StockRequestItems->newEntity([
                    'stock_request_id' => $stockRequestId,
                    'product_id' => $item['product_id'],
                    'product_variant_id' => $item['product_variant_id'] ?? null,
                    'product_attribute_id' => $item['product_attribute_id'] ?? null,
                    'requested_quantity' => $item['quantity'],
                    'supervisor_approved_quantity' => strtolower($role->name) === 'showroom supervisor' ? $item['accepted_quantity'] : null,
                    'manager_approved_quantity' => strtolower($role->name) !== 'showroom supervisor' ? $item['accepted_quantity'] : null,
                    'fulfilled_quantity' => $item['accepted_quantity'],
                    'status' => $status
                ]);

                if (!$this->StockRequestItems->save($stockRequestItem)) {
                    return $this->response->withStatus(500)->withStringBody(__('Failed to approve stock request item.'));
                }

                if ($status !== 'Rejected') {
                    // Update product stock reserved_quantity
                    $this->updateProductStocks(
                        strtolower($stockRequest->requestor_type),
                        $stockRequest->to_showroomID ?? null,
                        $stockRequest->warehouse_id ?? null,
                        $item['product_id'] ?? null,
                        $item['product_variant_id'] ?? null,
                        $item['product_attribute_id'] ?? null,
                        $item['accepted_quantity'] ?? 0
                    );
                }
            }

            if($allRejected)
            {
                // Send approval email
                $this->sendStockRequestRejectionEmail($stockRequest);

                return $this->response->withStatus(200)->withType('application/json')->withStringBody(json_encode([
                    'success' => true,
                    'message' => __('Stock request rejected successfully.')
                ]));

            }else
            {
                // Send approval email
                $this->sendStockRequestApprovalEmail($stockRequest);

                return $this->response->withStatus(200)->withType('application/json')->withStringBody(json_encode([
                    'success' => true,
                    'message' => __('Stock request approved successfully.')
                ]));
            }

        } catch (\Exception $e) {
            return $this->response->withStatus(500)->withStringBody($e->getMessage());
        }
    }

    //ZAID
    private function sendStockRequestRejectionEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Requesting Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);

        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if ($requestorEmail) {
            $toEmails[] = $requestorEmail; // Send only to requesting showroom manager
        }

        // Set destination info for display (only needed for email body context)
        if (!empty($stock_request->to_showroomID)) {
            $toShowroom = $this->Showrooms->get($stock_request->to_showroomID);
            $toLocation = "Showroom: {$toShowroom->name}";
        } elseif (!empty($stock_request->warehouse_id)) {
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id);
            $toLocation = "Warehouse: {$warehouse->name}";
        } else {
            $toLocation = "N/A";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipient found for rejected stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Rejected',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => "Dear Manager,",
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-Y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Rejected";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_request_rejection', // You can keep using 'stock_request_approval' if no separate template is needed
            $emailData
        );
    }

    //ZAID
    private function sendStockRequestApprovalEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Requesting Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);

        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Request Approval
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Request Approval
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for approved stock request ID: " . $stock_request->id);
            return;
        }

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Approved',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'request_date' => $stock_request->created ? $stock_request->created->format('d-m-Y') : 'N/A',
        ];

        $subject = "Stock Request #{$stock_request->id} - Approved";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_request_approval',
            $emailData
        );
    }

    //ZAID
    public function getIncomingStocks()
    {
        if ($this->request->is('get')) {
            // Get the logged-in user details
            $identity = $this->request->getAttribute('identity');

            if (!$identity) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 401,
                    'message' => 'Unauthorized access'
                ]));
            }

            // Extract user details
            $roleId = $identity->get('role_id');
            $userId = $identity->get('id');

            // Fetch the user's role
            $role = $this->Users->Roles->get($roleId);
            $roleName = strtolower($role->name);

            // Initialize query
            $stockMovementsQuery = $this->StockMovements->find()
                ->select([
                    'StockMovements.id',
                    'StockMovements.movement_type',
                    'StockMovements.movement_date',
                    'StockMovements.created',
                    'sender' => $this->StockMovements->query()->newExpr()->add([
                        "CASE 
                            WHEN StockRequests.to_showroomID IS NULL THEN Warehouses.name 
                            ELSE DestinationShowrooms.name 
                        END"
                    ]),
                    'receiver' => 'ReceiverShowrooms.name'
                ])
                ->join([
                    'StockRequests' => [
                        'table' => 'stock_requests',
                        'type' => 'INNER',
                        'conditions' => 'StockMovements.referenceID = StockRequests.id',
                    ],
                    'DestinationShowrooms' => [
                        'table' => 'showrooms',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.to_showroomID = DestinationShowrooms.id',
                    ],
                    'Warehouses' => [
                        'table' => 'warehouses',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.warehouse_id = Warehouses.id',
                    ],
                    'ReceiverShowrooms' => [
                        'table' => 'showrooms',
                        'type' => 'LEFT',
                        'conditions' => 'StockRequests.showroom_id = ReceiverShowrooms.id',
                    ],
                ])
                ->where(['StockMovements.movement_type' => 'Incoming']);

            // Role-based filtering
            if ($roleName === 'showroom manager') {
                // Fetch manager's assigned showroom
                $managerShowroom = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_manager' => $userId])
                    ->first();

                if ($managerShowroom) {
                    $stockMovementsQuery->where(['StockMovements.showroom_id' => $managerShowroom->id]);
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'success',
                        'data' => [],
                        'message' => 'No incoming stock found for this showroom manager'
                    ]));
                }
            } elseif ($roleName === 'showroom supervisor') {
                // Fetch all showrooms assigned to this supervisor
                $supervisorShowrooms = $this->Showrooms->find()
                    ->select(['id'])
                    ->where(['showroom_supervisor' => $userId])
                    ->toArray();

                if (!empty($supervisorShowrooms)) {
                    $supervisorShowroomIds = array_map(fn($showroom) => $showroom->id, $supervisorShowrooms);
                    $stockMovementsQuery->where(['StockMovements.showroom_id IN' => $supervisorShowroomIds]);
                } else {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'success',
                        'data' => [],
                        'message' => 'No incoming stock found for this supervisor'
                    ]));
                }
            } else {
                // General condition for other roles
                $stockMovementsQuery->where(['StockMovements.showroom_id IS NOT' => null]);
            }

            $stockMovementsQuery->order(['StockMovements.id' => 'DESC']);
            $stockMovements = $stockMovementsQuery->toArray();

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'success',
                'data' => $stockMovements
            ]));
        } else {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 405,
                'message' => 'Method not allowed'
            ]));
        }
    }

    //ZAID
    public function viewIncomingStock()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['stock_movement_id'])) {
                $result = [
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('Stock Movement ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $stockMovementId = $data['stock_movement_id'];

                // Fetch Stock Movement Details
                $stockMovement = $this->StockMovements->find()
                    ->select([
                        'StockMovements.id',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'StockMovements.created',
                        'sender' => $this->StockMovements->query()->newExpr()->add([
                            "CASE 
                                WHEN StockRequests.to_showroomID IS NULL THEN Warehouses.name 
                                ELSE DestinationShowrooms.name 
                            END"
                        ]),
                        'receiver' => 'ReceiverShowrooms.name'
                    ])
                    ->join([
                        'StockRequests' => [
                            'table' => 'stock_requests',
                            'type' => 'INNER',
                            'conditions' => 'StockMovements.referenceID = StockRequests.id',
                        ],
                        'DestinationShowrooms' => [
                            'table' => 'showrooms',
                            'type' => 'LEFT',
                            'conditions' => 'StockRequests.to_showroomID = DestinationShowrooms.id',
                        ],
                        'Warehouses' => [
                            'table' => 'warehouses',
                            'type' => 'LEFT',
                            'conditions' => 'StockRequests.warehouse_id = Warehouses.id',
                        ],
                        'ReceiverShowrooms' => [
                            'table' => 'showrooms',
                            'type' => 'LEFT',
                            'conditions' => 'StockRequests.showroom_id = ReceiverShowrooms.id',
                        ],
                    ])
                    ->where(['StockMovements.id' => $stockMovementId])
                    ->first();

                if (!$stockMovement) {
                    $result = [
                        'status' => 'error',
                        'code' => 404,
                        'message' => __('Stock movement not found')
                    ];
                    $this->response = $this->response->withStatus(404);
                } else {
                    // Fetch Stock Movement Items
                    $stockMovementItems = $this->StockMovements->StockMovementItems->find()
                        ->select([
                            'StockMovementItems.id',
                            'StockMovementItems.quantity',
                            'StockMovementItems.product_variant_id',
                            'StockMovementItems.product_attribute_id',
                            'StockMovementItems.stock_movement_id',
                            'Products.name',
                            'Products.purchase_price',
                            'Products.sku',
                            'ProductVariants.id',
                            'ProductVariants.variant_name',
                            'ProductVariants.purchase_price',
                            'ProductVariants.sku',
                        ])
                        ->leftJoinWith('Products')
                        ->leftJoinWith('ProductVariants')
                        ->where(['StockMovementItems.stock_movement_id' => $stockMovementId])
                        ->toArray();

                    foreach ($stockMovementItems as &$item) {
                        $item->attributes = [];

                        if ($item->product_attribute_id) {
                            $attributes = $this->ProductAttributes->find()
                                ->where(['ProductAttributes.id' => $item->product_attribute_id])
                                ->contain([
                                    'Attributes' => ['fields' => ['Attributes.name']],
                                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                                ])
                                ->first();

                            if ($attributes) {
                                $item->attributes = [
                                    'attribute_name' => $attributes->attribute->name ?? '',
                                    'attribute_value' => $attributes->attribute_value->value ?? ''
                                ];
                            }
                        }
                    }

                    $result = [
                        'status' => 'success',
                        'data' => [
                            'stock_movement' => $stockMovement,
                            'stock_movement_items' => $stockMovementItems
                        ]
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getOutgoingStocks()
    {
        $this->request->allowMethod(['get']); // Ensure it is a GET request

        // Get the logged-in user details
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStatus(401)->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Extract user details
        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Fetch the user's role
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);

        // Initialize stock movements array
        $stock_movements = [];

        $stockMovementsQuery = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.created',
                'Showroom.name', // From Showroom (source)
                'ToShowroom.name', // To Showroom (destination)
                'total_items' => $this->StockMovements->StockMovementItems->find()
                    ->func()
                    ->sum('StockMovementItems.quantity'),
                'total_value' => $this->StockMovements->StockMovementItems->find()
                    ->func()
                    ->sum(
                        '(CASE 
                            WHEN StockMovementItems.product_variant_id IS NULL 
                                THEN Products.purchase_price * StockMovementItems.quantity 
                            ELSE ProductVariants.purchase_price * StockMovementItems.quantity 
                         END)'
                    )
            ])
            ->leftJoinWith('StockMovementItems')
            ->leftJoinWith('StockMovementItems.Products')
            ->leftJoinWith('StockMovementItems.ProductVariants')
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                ['StockRequests.id = StockMovements.referenceId']
            )
            ->leftJoin(
                ['Showroom' => 'showrooms'], // From Showroom
                ['Showroom.id = StockMovements.showroom_id']
            )
            ->leftJoin(
                ['ToShowroom' => 'showrooms'], // To Showroom
                ['ToShowroom.id = StockRequests.showroom_id']
            );

        // Role-based filtering
        if ($roleName === 'showroom manager') {
            $managerShowroom = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_manager' => $userId])
                ->first();

            if ($managerShowroom) {
                $stockMovementsQuery->where([
                    'StockMovements.movement_type' => 'Outgoing',
                    'StockMovements.showroom_id' => $managerShowroom->id
                ]);
            } else {
                return $this->response->withType('application/json')->withStatus(404)->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 404,
                    'message' => 'No stock movements found'
                ]));
            }
        } elseif ($roleName === 'showroom supervisor') {
            $supervisorShowrooms = $this->Showrooms->find()
                ->select(['id'])
                ->where(['showroom_supervisor' => $userId])
                ->toArray();

            if (!empty($supervisorShowrooms)) {
                $showroomIds = array_column($supervisorShowrooms, 'id');

                $stockMovementsQuery->where([
                    'StockMovements.movement_type' => 'Outgoing',
                    'StockMovements.showroom_id IN' => $showroomIds
                ]);
            } else {
                return $this->response->withType('application/json')->withStatus(404)->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 404,
                    'message' => 'No stock movements found'
                ]));
            }
        } else {
            // General condition for other roles
            $stockMovementsQuery->where([
                'StockMovements.movement_type' => 'Outgoing',
                'StockMovements.showroom_id IS NOT' => null,
            ]);
        }

        // Grouping and ordering
        $stockMovementsQuery->group([
            'StockMovements.id',
            'StockMovements.movement_type',
            'StockMovements.movement_date',
            'StockMovements.created',
            'Showroom.name',
            'ToShowroom.name'
        ])
        ->order(['StockMovements.id' => 'DESC']);

        // Execute query
        $stock_movements = $stockMovementsQuery->toArray();

        if (empty($stock_movements)) {
            return $this->response->withType('application/json')->withStatus(404)->withStringBody(json_encode([
                'status' => 'error',
                'code' => 404,
                'message' => 'No stock movements found'
            ]));
        }

        return $this->response->withType('application/json')
            ->withStatus(200)
            ->withStringBody(json_encode([
                'status' => 'success',
                'data' => $stock_movements
            ]));
    }

    //ZAID
    public function viewOutgoingStock()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['stock_movement_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Stock Movement ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $id = $data['stock_movement_id'];

                // Fetch StockMovement details
                $StockMovement = $this->StockMovements->find()
                    ->select([
                        'StockMovements.id',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'StockMovements.created',
                        'Showroom.name',
                        'ToShowroom.name',
                        'DriverUser.first_name',
                        'DriverUser.last_name'
                    ])
                    ->leftJoin([
                        'StockRequests' => 'stock_requests'
                    ], ['StockRequests.id = StockMovements.referenceId'])
                    ->leftJoin([
                        'Showroom' => 'showrooms'
                    ], ['Showroom.id = StockMovements.showroom_id'])
                    ->leftJoin([
                        'ToShowroom' => 'showrooms'
                    ], ['ToShowroom.id = StockRequests.showroom_id'])
                    ->leftJoin([
                        'Drivers' => 'drivers'
                    ], ['Drivers.id = StockMovements.driver_id'])
                    ->leftJoin([
                        'DriverUser' => 'users'
                    ], ['DriverUser.id = Drivers.user_id'])
                    ->where(['StockMovements.id' => $id])
                    ->first();

                // Fetch StockMovementItems
                $StockMovementItems = $this->StockMovements->StockMovementItems->find()
                    ->select([
                        'StockMovementItems.id',
                        'StockMovementItems.quantity',
                        'StockMovementItems.product_variant_id',
                        'StockMovementItems.product_attribute_id',
                        'StockMovementItems.stock_movement_id',
                        'Products.name',
                        'Products.purchase_price',
                        'Products.sku',
                        'ProductVariants.id',
                        'ProductVariants.variant_name',
                        'ProductVariants.purchase_price',
                        'ProductVariants.sku'
                    ])
                    ->leftJoinWith('Products')
                    ->leftJoinWith('ProductVariants')
                    ->where(['StockMovementItems.stock_movement_id' => $id])
                    ->toArray();

                foreach ($StockMovementItems as &$item) {
                    $item->attributes = [];
                    if ($item->product_attribute_id) {
                        $attributes = $this->ProductAttributes->find()
                            ->where(['ProductAttributes.id' => $item->product_attribute_id])
                            ->contain([
                                'Attributes' => ['fields' => ['Attributes.name']],
                                'AttributeValues' => ['fields' => ['AttributeValues.value']]
                            ])
                            ->first();
                        if ($attributes) {
                            $item->attributes = [
                                'attribute_name' => $attributes->attribute->name ?? '',
                                'attribute_value' => $attributes->attribute_value->value ?? ''
                            ];
                        }
                    }
                }

                $result = [
                    'status' => __('success'),
                    'StockMovement' => $StockMovement,
                    'StockMovementItems' => $StockMovementItems
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID 
    public function getProducts()
    {
        if ($this->request->is('get')) {

            $products = $this->Products->find()
                ->select(['id', 'name', 'sku'])
                ->where(['Products.status' => 'A'])
                ->order(['Products.name' => 'ASC'])
                ->toArray();

            if (!empty($products)) {
                $result = [
                    'status' => 'success',
                    'data' => $products
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                $result = [
                    'status' => 'success',
                    'data' => [],
                    'message' => __('No products found')
                ];
                $this->response = $this->response->withStatus(200);
            }
            
        } else {
            $result = [
                'status' => 'error',
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getVariants()
    {
        if (!$this->request->is('post')) {
            return $this->response->withStatus(405)->withStringBody(__('Method not allowed.'));
        }

        $data = $this->request->getData();
        $productId = $data['product_id'] ?? null;
        $requestTo = $data['request_to'] ?? null;
        $requestId = $data['id'] ?? null;

        if (!$productId || !$requestTo || !$requestId) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Invalid request parameters']));
        }

        $response = [
            'variants' => [],
            'attributes' => [],
            'quantity' => 0
        ];

        $stockCondition = ['ProductStocks.product_id' => $productId];

        if ($requestTo === 'warehouse') {
            $stockCondition['ProductStocks.warehouse_id'] = $requestId;
        } elseif ($requestTo === 'showroom') {
            $stockCondition['ProductStocks.showroom_id'] = $requestId;
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : ''; 

        // Fetch Variants
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku', 'promotion_price'])
            ->where(['ProductVariants.product_id' => $productId, 'ProductVariants.status' => 'A'])
            ->toArray();

        foreach ($variants as $variant) {

            $promotionPrice = is_numeric($variant->promotion_price) ? (float)$variant->promotion_price : 0;

            $formattedPromotionPrice = number_format($promotionPrice, 2, $decimalSeparator, $thousandSeparator) . ' ' . $currencySymbol;

            $variantStockCondition = $stockCondition;
            $variantStockCondition['ProductStocks.product_variant_id'] = $variant->id;

            $variantStock = $this->ProductStocks->find()
                ->select([
                    'available_stock' => $this->ProductStocks->find()->func()->coalesce([
                        new QueryExpression('quantity - reserved_stock'),
                        0
                    ])
                ])
                ->where($variantStockCondition)
                ->first();

            $quantity = $variantStock ? $variantStock->available_stock : 0;

            // Fetch Attributes
            $attributes = $this->ProductAttributes->find()
                ->contain(['Attributes', 'AttributeValues'])
                ->where([
                    'ProductAttributes.product_id' => $productId,
                    'ProductAttributes.status' => 'A'
                ])
                ->toArray();

            $attributeData = [];

            foreach ($attributes as $attribute) {
                $attributeStockCondition = $variantStockCondition;
                $attributeStockCondition['ProductStocks.product_attribute_id'] = $attribute->id;

                $attributeStock = $this->ProductStocks->find()
                    ->select([
                        'available_stock' => new QueryExpression('quantity - reserved_stock')
                    ])
                    ->where($attributeStockCondition)
                    ->first();

                $attributeQuantity = $attributeStock ? $attributeStock->available_stock : 0;

                $attributeData[] = [
                    'attribute_id' => $attribute->id,
                    'attribute_name' => $attribute->attribute->name,
                    'attribute_value' => $attribute->attribute_value->value,
                    'quantity' => $attributeQuantity
                ];
            }

            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
                'quantity' => $quantity,
                'promotion_price' => $formattedPromotionPrice,
                'attributes' => $attributeData,
            ];
        }

        // Handle case when no variants exist
        if (empty($response['variants'])) {
            $totalStock = $this->ProductStocks->find()
                ->select([
                    'available_stock' => new QueryExpression('quantity - reserved_stock')
                ])
                ->where($stockCondition)
                ->first();

            $response['quantity'] = $totalStock ? $totalStock->available_stock : 0;
        }

        $this->set([
            'response' => $response,
            '_serialize' => ['response'],
        ]);

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    //ZAID
    public function incomingStockToShowroom()
    {
        $this->request->allowMethod(['post']);
        $this->loadComponent('Stock'); // Load the Stock component

        $data = $this->request->getData();

        if (empty($data['stock_request_id']) || empty($data['movement_date']) || empty($data['to_showroom_id'])) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Missing required parameters']));
        }

        $stockRequestId = $data['stock_request_id'];
        $movementDate = $data['movement_date'];
        $showroomId = $data['to_showroom_id'];

        // Fetch stock request details
        $stockRequest = $this->StockRequests->find()
            ->where(['StockRequests.id' => $stockRequestId])
            ->contain([
                'StockRequestItems' => function ($q) {
                    return $q->where(['StockRequestItems.status' => 'Approved']);
                }
            ])
            ->first();

        if (!$stockRequest) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Stock request not found']));
        }

        // Prepare stock items
        $stockItems = [];
        foreach ($stockRequest->stock_request_items as $item) {
            $stockItems[] = [
                'product_id' => $item->product_id,
                'product_variant_id' => $item->product_variant_id,
                'product_attribute_id' => $item->product_attribute_id,
                'quantity' => $item->fulfilled_quantity
            ];
        }

        // Call the Stock component's method
        $result = $this->Stock->addShowroomInStock($showroomId, $movementDate, 'stock_request', $stockRequestId, $stockItems, null, null);

        if ($result) {
            // Update stock request status to 'Completed'
            $stockRequest->request_status = 'Completed';
            $this->StockRequests->save($stockRequest);

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['success' => 'Stock successfully updated']));
        } else {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['error' => 'Failed to process stock request']));
        }
    }

    public function outgoingStockFromShowroom()
    {
        $this->autoRender = false; // Disable view rendering for API response

        if ($this->request->is('post')) {
            $this->loadComponent('Stock'); // Load the Stock component

            $data = $this->request->getData();

            if (empty($data['stock_request_id'])) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Stock request ID is required'
                ]));
            }

            $stockRequestId = $data['stock_request_id'];
            $driverId = $data['driver_id'] ?? null;

            // Fetch stock request details
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain([
                    'StockRequestItems' => function ($q) {
                        return $q->where(['StockRequestItems.status' => 'Approved']);
                    }
                ])
                ->first();

            if (!$stockRequest) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Stock request not found'
                ]));
            }

            // Prepare parameters for the Stock component
            $showroom_id = $stockRequest->to_showroomID;
            $movement_date = date('Y-m-d');
            $reference_type = 'stock_request';
            $referenceID = $stockRequestId;

            // Prepare stock items
            $stock_items = [];
            foreach ($stockRequest->stock_request_items as $item) {
                $stock_items[] = [
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_attribute_id' => $item->product_attribute_id,
                    'quantity' => $item->fulfilled_quantity ?? 0
                ];
            }

            // Call the Stock component's method
            $result = $this->Stock->addShowroomOutStock(
                $showroom_id, 
                $movement_date, 
                $reference_type, 
                $referenceID, 
                $stock_items, 
                null, 
                $driverId
            );

            if ($result) {

                $this->StockMovements->updateAll(
                    ['verify_status' => 'Approved'],
                    ['referenceID' => $stockRequestId, 'reference_type' => 'stock_request']
                );

                // Send dispatch email
                $this->sendStockDispatchEmail($stockRequest);

                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Stock dispatched successfully'
                ]));
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Failed to process stock request. Please try again.'
                ]));
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'error',
            'message' => 'Invalid request method'
        ]));
    }

    private function sendStockDispatchEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Dispatching Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);
        
        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Dispatch
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Dispatch
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Users']
            ]);

            $warehouseManagerEmail = $warehouse->user ? $warehouse->user->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear Manager,";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for dispatched stock request ID: " . $stock_request->id);
            return;
        }

        $toEmails[] = '<EMAIL>';

        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Dispatched',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'dispatch_date' => date('d-m-Y')
        ];

        $subject = "Stock Dispatch #{$stock_request->id} - Completed";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_dispatch_notification',
            $emailData
        );
    }

    //ZAID
    public function getIncomingRequestId()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['showroom_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Showroom ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $toShowroomId = $data['showroom_id'];

                // Fetch stock requests where requestor_type is Warehouse
                $warehouseRequests = $this->StockRequests->find()
                    ->select([
                        'id',
                        'warehouse_name' => 'Warehouses.name'
                    ])
                    ->contain(['Warehouses'])
                    ->where([
                        'StockRequests.showroom_id' => $toShowroomId,
                        'StockRequests.warehouse_id IS NOT' => null,
                        'StockRequests.requestor_type' => 'Showroom',
                        'StockRequests.status' => 'A',
                        'StockRequests.request_status' => 'Approved'
                    ])
                    ->order(['StockRequests.request_date' => 'DESC'])
                    ->toArray();

                $filteredWarehouseRequests = [];
                    foreach ($warehouseRequests as $request) {
                        $movementExists = $this->StockMovements->exists([
                            'referenceID' => $request->id,
                            'movement_type' => 'Outgoing',
                            'verify_status' => 'Approved'
                        ]);

                        if ($movementExists) {
                            $filteredWarehouseRequests[] = $request;
                        }
                    }

                // Fetch stock requests where requestor_type is Showroom
                $showroomRequests = $this->StockRequests->find()
                    ->select([
                        'id',
                        'showroom_name' => 'ToShowroom.name',
                    ])
                    ->contain(['Showrooms'])
                    ->where([
                        'StockRequests.showroom_id' => $toShowroomId,
                        'StockRequests.to_showroomID IS NOT' => null,
                        'StockRequests.requestor_type' => 'Showroom',
                        'StockRequests.status' => 'A',
                        'StockRequests.request_status' => 'Approved'
                    ])
                    ->leftJoin(
                        ['ToShowroom' => 'showrooms'],
                        ['ToShowroom.id = StockRequests.to_showroomID']
                    )
                    ->order(['StockRequests.request_date' => 'DESC'])
                    ->toArray();

                $filteredShowroomRequests = [];
                    foreach ($showroomRequests as $request) {
                        $movementExists = $this->StockMovements->exists([
                            'referenceID' => $request->id,
                            'movement_type' => 'Outgoing',
                            'verify_status' => 'Approved'
                        ]);

                        if ($movementExists) {
                            $filteredShowroomRequests[] = $request;
                        }
                    }

                $result = [
                    'status' => __('success'),
                    'data' => [
                        'warehouse_requests' => $filteredWarehouseRequests,
                        'showroom_requests' => $filteredShowroomRequests
                    ]
                ];
                $this->response = $this->response->withStatus(200);
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getStockRequestItems()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['stock_request_id'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Stock Request ID is required')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $stockRequestId = $data['stock_request_id'];

                $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                    ->select([
                        'StockRequestItems.id',
                        'StockRequestItems.fulfilled_quantity',
                        'StockRequestItems.product_id',
                        'StockRequestItems.product_variant_id',
                        'StockRequestItems.product_attribute_id',
                        'StockRequestItems.stock_request_id',
                        'StockRequestItems.supervisor_approved_quantity',
                        'Products.name',
                        'Products.promotion_price', 
                        'Products.sku', 
                        'Products.supplier_id',
                        'ProductVariants.id', 
                        'ProductVariants.variant_name', 
                        'ProductVariants.promotion_price', 
                        'ProductVariants.sku', 
                        'Suppliers.name' 
                    ])
                    ->leftJoinWith('Products')
                    ->leftJoinWith('Products.Suppliers')
                    ->leftJoinWith('ProductVariants')
                    ->where([
                        'StockRequestItems.stock_request_id' => $stockRequestId,
                        'StockRequestItems.status' => 'Approved'
                    ])
                    ->toArray();

                if (empty($stockRequestItems)) {
                    $result = [
                        'status' => __('error'),
                        'code' => 404,
                        'message' => __('No Stock Request Items found')
                    ];
                    $this->response = $this->response->withStatus(404);
                } else {
                    $response = [];
                    foreach ($stockRequestItems as $item) {
                        $item->attributes = [];

                        if ($item->product_attribute_id) {
                            $attributes = $this->ProductAttributes->find()
                                ->where(['ProductAttributes.id' => $item->product_attribute_id])
                                ->contain([
                                    'Attributes' => ['fields' => ['Attributes.name']],
                                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                                ])
                                ->first();

                            if ($attributes) {
                                $item->attributes = [
                                    'attribute_name' => $attributes->attribute->name ?? '',
                                    'attribute_value' => $attributes->attribute_value->value ?? ''
                                ];
                            }
                        }

                        $sku = $item->product_variant_id 
                            ? $item->_matchingData['ProductVariants']->sku 
                            : $item->_matchingData['Products']->sku;

                        $response[] = [
                            'id' => $item->id,
                            'product_name' => $item->_matchingData['Products']->name,
                            'product_variant' => $item->product_variant_id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A',
                            'product_attribute' => $item->attributes ? ($item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value']) : 'N/A',
                            'sku' => $sku,
                            'fulfilled_quantity' => $item->fulfilled_quantity,
                            'supplier_name' => $item->_matchingData['Suppliers']->name ?? 'N/A'
                        ];
                    }

                    $result = [
                        'status' => __('success'),
                        'data' => $response
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    // public function getOutgoingRequestId()
    // {
    //     if ($this->request->is('post')) {
    //         $data = $this->request->getData();

    //         if (empty($data['showroom_id']) || empty($data['to_showroomId'])) {
    //             $result = [
    //                 'status' => __('error'),
    //                 'code' => 400,
    //                 'message' => __('Both warehouse and showroom must be selected.')
    //             ];
    //             $this->response = $this->response->withStatus(400);
    //         } else {
    //             $showroom_id = $data['showroom_id'];
    //             $to_showroomId = $data['to_showroomId'];

    //             // Fetch stock requests that meet the conditions
    //             $requests = $this->StockRequests->find()
    //                 ->select(['id'])
    //                 ->where([
    //                     'StockRequests.requestor_type' => 'Showroom',
    //                     'StockRequests.request_status' => 'Approved',
    //                     'StockRequests.showroom_id' => $to_showroomId,
    //                     'StockRequests.to_showroomID' => $showroom_id,
    //                 ])
    //                 ->all();

    //             // Extract request IDs
    //             $requestIds = array_map(fn($request) => $request->id, $requests->toArray());

    //             if (!empty($requestIds)) {
    //                 // Check if request IDs exist in stock_movements.referenceID
    //                 $existingReferences = $this->StockMovements->find()
    //                     ->select(['referenceID'])
    //                     ->where(['StockMovements.referenceID IN' => $requestIds])
    //                     ->all();

    //                 // Extract existing reference IDs
    //                 $existingReferenceIds = array_map(fn($movement) => $movement->referenceID, $existingReferences->toArray());

    //                 // Filter request IDs that are not in stock_movements
    //                 $filteredRequestIds = array_diff($requestIds, $existingReferenceIds);
    //             } else {
    //                 // No stock requests found
    //                 $filteredRequestIds = [];
    //             }

    //             if (empty($filteredRequestIds)) {
    //                 // If no valid stock requests found, return an error message
    //                 $result = [
    //                     'status' => __('error'),
    //                     'code' => 404,
    //                     'message' => __('Stock ID not found.')
    //                 ];
    //                 $this->response = $this->response->withStatus(404);
    //             } else {
    //                 // Format response
    //                 $formattedRequestIds = array_map(fn($id) => ['id' => $id], $filteredRequestIds);

    //                 $result = [
    //                     'status' => __('success'),
    //                     'data' => $formattedRequestIds
    //                 ];
    //                 $this->response = $this->response->withStatus(200);
    //             }
    //         }
    //     } else {
    //         $result = [
    //             'status' => __('error'),
    //             'code' => 405,
    //             'message' => __('Method not allowed')
    //         ];
    //         $this->response = $this->response->withStatus(405);
    //     }

    //     $this->set(['result' => $result]);
    //     $this->viewBuilder()->setOption('serialize', ['result']);
    // }

    public function getOutgoingRequestId()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();

            if (empty($data['showroom_id']) || empty($data['to_showroomId'])) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Both warehouse and showroom must be selected.')
                ];
                $this->response = $this->response->withStatus(400);
            } else {
                $showroom_id = $data['showroom_id'];
                $to_showroomId = $data['to_showroomId'];

                // Fetch stock requests that meet the conditions
                $requests = $this->StockRequests->find()
                    ->select(['id'])
                    ->where([
                        'StockRequests.requestor_type' => 'Showroom',
                        'StockRequests.request_status' => 'Approved',
                        'StockRequests.showroom_id' => $to_showroomId,
                        'StockRequests.to_showroomID' => $showroom_id,
                    ])
                    ->all();

                // Extract request IDs
                $requestIds = array_map(fn($request) => $request->id, $requests->toArray());

                if (!empty($requestIds)) {
                    // Check if request IDs exist in stock_movements.referenceID
                    $existingReferences = $this->StockMovements->find()
                        ->select(['referenceID'])
                        ->where(['StockMovements.referenceID IN' => $requestIds])
                        ->all();

                    // Extract existing reference IDs
                    $existingReferenceIds = array_map(fn($movement) => $movement->referenceID, $existingReferences->toArray());

                    // Filter request IDs that are not in stock_movements
                    $filteredRequestIds = array_diff($requestIds, $existingReferenceIds);
                } else {
                    // No stock requests found
                    $filteredRequestIds = [];
                }

                if (empty($filteredRequestIds)) {
                    // If no valid stock requests found, return an error message
                    $result = [
                        'status' => __('error'),
                        'code' => 404,
                        'message' => __('Stock ID not found.')
                    ];
                    $this->response = $this->response->withStatus(404);
                } else {
                    // Re-index the array to avoid numeric keys in JSON object
                    $formattedRequestIds = array_map(fn($id) => ['id' => $id], array_values($filteredRequestIds));

                    $result = [
                        'status' => __('success'),
                        'data' => $formattedRequestIds
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    //ZAID
    public function getDrivers()
    {
        if ($this->request->is('get')) {
            // Query the Drivers table and join with Users to get driver names
            $query = $this->Drivers->find()
                ->select(['Drivers.id', 'Users.first_name', 'Users.last_name'])
                ->where(['Users.status' => 'A', 'Users.user_type' => 'Driver'])
                ->contain(['Users']);

            $driversList = $query->toArray();

            $drivers = [];
            foreach ($driversList as $driver) {
                $drivers[] = [
                    'id' => $driver->id,
                    'name' => $driver->user->first_name . ' ' . $driver->user->last_name
                ];
            }

            $result = [
                'status' => __('success'),
                'data' => $drivers
            ];
            $this->response = $this->response->withStatus(200);
        } else {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            $this->response = $this->response->withStatus(405);
        }

        $this->set(['result' => $result]);
        $this->viewBuilder()->setOption('serialize', ['result']);
    }

    public function deleteStockRequest()
    {
        $this->request->allowMethod(['post']); // Only allow POST requests

        // Get logged-in user details
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Get request data
        $data = $this->request->getData();
        $id = isset($data['stock_request_id']) ? $data['stock_request_id'] : null;

        if (!$id) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Stock request ID is required'
            ]));
        }

        try {
            // Fetch the stock request record
            $stockRequest = $this->StockRequests->find()
                ->where(['id' => $id, 'status !=' => 'D'])
                ->first();

            if (!$stockRequest) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Stock request not found or already deleted'
                ]));
            }

            // Update status to 'D' (Deleted)
            $stockRequest->status = 'D';

            if ($this->StockRequests->save($stockRequest)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Stock request has been marked as deleted'
                ]));
            }

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Failed to delete stock request. Please try again.'
            ]));
        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]));
        }
    }

    public function addLock()
    {
        $this->request->allowMethod(['post', 'put']);

        // Get JSON input
        $data = $this->request->getData();

        // Validate input
        if (empty($data['stock_request_id'])) {
            return $this->response->withType('application/json')
                ->withStatus(400)
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Stock request ID is required.'
                ]));
        }

        try {
            // Fetch stock request with items
            $stock_request = $this->StockRequests->get($data['stock_request_id'], [
                'contain' => ['StockRequestItems']
            ]);
        } catch (\Exception $e) {
            return $this->response->withType('application/json')
                ->withStatus(404)
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Stock request not found.'
                ]));
        }

        $requested_user = $this->Authentication->getIdentity();

        $editLockTime = new FrozenTime($stock_request->edit_lock_time);
        $now = FrozenTime::now();
        $minutesPassed = $editLockTime->diffInMinutes($now);

        // Check if locked by another user and within lock time (10 minutes)
        if (
            !empty($stock_request->edit_lock_by) &&
            $stock_request->edit_lock_by != $requested_user->id &&
            $minutesPassed < 10
        ) {
            return $this->response->withType('application/json')
                ->withStatus(403)
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'This request is currently being edited/validated.'
                ]));
        }

        // Lock it for current user
        $stock_request->edit_lock_by = $requested_user->id;
        $stock_request->edit_lock_time = $now;

        if ($this->StockRequests->save($stock_request)) {
            return $this->response->withType('application/json')
                ->withStatus(200)
                ->withStringBody(json_encode([
                    'success' => true,
                    'message' => 'Stock request successfully locked.',
                    'lock_by' => $requested_user->id,
                    'lock_time' => $now->i18nFormat('yyyy-MM-dd HH:mm:ss')
                ]));
        } else {
            return $this->response->withType('application/json')
                ->withStatus(500)
                ->withStringBody(json_encode([
                    'success' => false,
                    'message' => 'Failed to lock the stock request.'
                ]));
        }
    }

    public function editStockRequest()
    {
        $this->request->allowMethod(['post', 'put']); // Only allow POST and PUT requests

        // Decode JSON request body
        $data = $this->request->getData();

        // Validate if `stock_request_id` is provided
        if (empty($data['stock_request_id'])) {
            return $this->response->withType('application/json')
                ->withStatus(400)
                ->withStringBody(json_encode(['success' => false, 'message' => 'Stock request ID is required.']));
        }

        // Fetch stock request by ID
        $stock_request = $this->StockRequests->get($data['stock_request_id'], ['contain' => ['StockRequestItems']]);

        if (!$stock_request) {
            return $this->response->withType('application/json')
                ->withStatus(404)
                ->withStringBody(json_encode(['success' => false, 'message' => 'Stock request not found.']));
        }

        $requested_user = $this->Authentication->getIdentity();

        // Validate Required Fields
        if (empty($data['product_id']) || empty($data['quantity'])) {
            return $this->response->withType('application/json')
                ->withStatus(400)
                ->withStringBody(json_encode(['success' => false, 'message' => 'Product IDs and quantities are required.']));
        }

        $stock_request->edit_lock_by = null;
        $stock_request->edit_lock_time = null;

        // Update Stock Request Status
        $stock_request = $this->StockRequests->patchEntity($stock_request, $data);

        if ($this->StockRequests->save($stock_request)) {

            // Delete old stock request items and insert new ones
            $this->StockRequests->StockRequestItems->deleteAll(['stock_request_id' => $data['stock_request_id']]);

            $stockRequestItems = [];
            foreach ($data['product_id'] as $index => $productId) {
                $stockRequestItems[] = [
                    'stock_request_id' => $data['stock_request_id'],
                    'product_id' => $productId,
                    'product_variant_id' => $data['product_variant_id'][$index] ?? null,
                    'product_attribute_id' => $data['product_attribute_id'][$index] ?? null,
                    'requested_quantity' => $data['quantity'][$index] ?? 0
                ];
            }

            $entities = $this->StockRequests->StockRequestItems->newEntities($stockRequestItems);
            $this->StockRequests->StockRequestItems->saveMany($entities);

            return $this->response->withType('application/json')
                ->withStatus(200)
                ->withStringBody(json_encode(['success' => true, 'message' => 'Stock request updated successfully.']));
        } else {
            return $this->response->withType('application/json')
                ->withStatus(500)
                ->withStringBody(json_encode(['success' => false, 'message' => 'Failed to update stock request. Please try again.']));
        }
    }

    /** WAREHOUSE STOCK MANAGEMENT API'S **/
    public function getWarehouseStockRequests()
    {
        $this->request->allowMethod(['get']); // Only allow GET requests

        // Get the authenticated user
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Extract user details
        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Fetch role name
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);

        $conditions = [
            'StockRequests.requestor_type' => 'Warehouse',
            'StockRequests.status IN' => ['A', 'I']
        ];

        // Role-based filtering
        if ($roleName === 'warehouse manager') {
            $warehouse = $this->Warehouses->find()
                ->select(['id'])
                ->where(['manager_id' => $userId])
                ->first();

            if ($warehouse) {
                $conditions['StockRequests.warehouse_id'] = $warehouse->id;
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No warehouse found for this manager'
                ]));
            }
        }

        // Fetch stock requests with joins
        $stockRequests = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.requestor_type',
                'StockRequests.request_status',
                'StockRequests.manager_review_status',
                'StockRequests.supervisor_verify_status',
                'StockRequests.status',
                'StockRequests.created',
                'Suppliers.name',
                'Warehouses.name',
                'SupplierPurchaseOrders.id',
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status'
            ])
            ->join([
                'table' => 'supplier_purchase_orders',
                'alias' => 'SupplierPurchaseOrders',
                'type' => 'INNER',
                'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            ])
            ->contain(['Suppliers', 'Warehouses'])
            ->where($conditions)
            ->order(['StockRequests.id' => 'DESC'])
            ->toArray();

        // Return formatted API response
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $stockRequests
        ]));
    }

    public function viewWarehouseStockRequests()
    {
        $this->request->allowMethod(['post']);

        $stockRequestId = $this->request->getData('stock_request_id');

        if (!$stockRequestId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Missing stock_request_id'
            ]));
        }

        // Fetch main stock request details
        $stockRequest = $this->StockRequests->find()
            ->select([
                'StockRequests.id',
                'StockRequests.request_date',
                'StockRequests.request_status',
                'StockRequests.supervisor_verified_time',
                'StockRequests.requested_by',
                'StockRequests.verified_by',
                'StockRequests.status',
                'StockRequests.supplier_id',
                'Warehouses.name',
                'Suppliers.name',
                'Users.first_name',
                'Users.last_name',
                'SupplierPurchaseOrders.id',
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date',
                'StockMovements.movement_date'
            ])
            ->join([
                [
                    'table' => 'supplier_purchase_orders',
                    'alias' => 'SupplierPurchaseOrders',
                    'type' => 'INNER',
                    'conditions' => 'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                ],
                [
                    'table' => 'stock_movements',
                    'alias' => 'StockMovements',
                    'type' => 'LEFT',
                    'conditions' => 'StockMovements.referenceID = StockRequests.id'
                ]
            ])
            ->contain(['Warehouses', 'Suppliers', 'Users'])
            ->where(['StockRequests.id' => $stockRequestId])
            ->first();

        if (!$stockRequest) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Stock request not found'
            ]));
        }

        // Fetch request items
        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price',
                'Products.sku',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.purchase_price',
                'ProductVariants.sku',
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $stockRequestId])
            ->toArray();

        // Enrich each item with supplier price and attributes
        foreach ($stockRequestItems as &$item) {
            $item->supplier_price = null;

            $supplierPriceQuery = $this->SupplierProducts->find()
                ->select(['supplier_price'])
                ->where([
                    'SupplierProducts.supplier_id' => $stockRequest->supplier_id,
                    'SupplierProducts.product_id' => $item->product_id
                ]);

            if ($item->product_variant_id) {
                $supplierPriceQuery->where(['SupplierProducts.product_variant_id' => $item->product_variant_id]);
            }

            $supplierPrice = $supplierPriceQuery->first();
            if ($supplierPrice) {
                $item->supplier_price = $supplierPrice->supplier_price;
            }

            $item->attributes = [];
            if ($item->product_attribute_id) {
                $attribute = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                if ($attribute) {
                    $item->attributes = [
                        'attribute_name' => $attribute->attribute->name ?? '',
                        'attribute_value' => $attribute->attribute_value->value ?? ''
                    ];
                }
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => true,
            'data' => [
                'stock_request' => $stockRequest,
                'items' => $stockRequestItems
            ]
        ]));
    }

    public function getWarehouseIncomingStocks()
    {
        $this->request->allowMethod(['get']); // Allow only GET requests

        // Get the authenticated user
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Extract user details
        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Get the role name
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);

        $conditions = ['StockMovements.movement_type' => 'Incoming'];

        // Handle Warehouse Manager and Assistant
        if ($roleName === 'warehouse manager' || $roleName === 'warehouse assistant') {
            $warehouseQuery = $this->Warehouses->find()
                ->select(['id']);

            if ($roleName === 'warehouse manager') {
                $warehouseQuery->where(['manager_id' => $userId]);
            } else {
                $warehouseQuery->where(['assistant_id' => $userId]);
            }

            $warehouse = $warehouseQuery->first();

            if ($warehouse) {
                $conditions['StockMovements.warehouse_id'] = $warehouse->id;
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No warehouse assigned to this user.'
                ]));
            }
        } else {
            // For other roles, only filter incoming with any warehouse set
            $conditions['StockMovements.warehouse_id IS NOT'] = null;
        }

        // Fetch the stock movements
        $stockMovements = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'Suppliers.name',
                'SupplierPurchaseOrders.id',
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status'
            ])
            ->contain(['Warehouses']) // Load warehouse details
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                'StockMovements.referenceID = StockRequests.id'
            )
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                'Suppliers.id = StockRequests.supplier_id'
            )
            ->leftJoin(
                ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            )
            ->where($conditions)
            ->order(['StockMovements.id' => 'DESC'])
            ->toArray();

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $stockMovements
        ]));
    }

    public function getSuppliers()
    {
        $this->request->allowMethod(['get']); // Only allow GET requests

        // Get the authenticated user
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        $roleId = $identity->get('role_id');
        $userId = $identity->get('id');

        // Fetch role name
        $role = $this->Users->Roles->get($roleId);
        $roleName = strtolower($role->name);

        $warehouseId = null;

        // Check for warehouse manager or assistant
        if ($roleName === 'warehouse manager' || $roleName === 'warehouse assistant') {
            $warehouseQuery = $this->Warehouses->find()->select(['id']);

            if ($roleName === 'warehouse manager') {
                $warehouseQuery->where(['manager_id' => $userId]);
            } elseif ($roleName === 'warehouse assistant') {
                $warehouseQuery->where(['assistant_id' => $userId]);
            }

            $warehouse = $warehouseQuery->first();

            if ($warehouse) {
                $warehouseId = $warehouse->id;
            } else {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'data' => [],
                    'message' => 'No warehouse found for this user.'
                ]));
            }
        }

        // Subquery to get stock movements
        $subquery = $this->StockMovements->find()
            ->select(['referenceID'])
            ->where(['referenceID IS NOT' => null]);

        // Main supplier query
        $suppliers = $this->Suppliers->find()
            ->where(['Suppliers.status' => 'A'])
            ->contain([
                'SupplierPurchaseOrders' => function ($q) use ($warehouseId, $subquery) {
                    $conditions = [
                        'SupplierPurchaseOrders.delivery_status' => 'Pending',
                        'SupplierPurchaseOrders.status' => 'A',
                        'SupplierPurchaseOrders.stock_request_id NOT IN' => $subquery
                    ];

                    if ($warehouseId) {
                        $conditions['SupplierPurchaseOrders.id_deliver_to'] = $warehouseId;
                    }

                    return $q->select([
                            'SupplierPurchaseOrders.supplier_id',
                            'SupplierPurchaseOrders.bill_no',
                            'SupplierPurchaseOrders.supplier_bill_no',
                            'SupplierPurchaseOrders.stock_request_id'
                        ])
                        ->where($conditions);
                }
            ])
            ->toArray();

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $suppliers
        ]));
    }

    public function getProductsBySupplier()
    {
        $this->request->allowMethod(['post']); // Allow only POST requests

        // Get the authenticated user
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Get supplierId from request body
        $data = $this->request->getData();
        $supplierId = $data['supplier_id'] ?? null;

        if (!$supplierId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 400,
                'message' => 'Missing supplier_id in request body.'
            ]));
        }

        // Fetch products related to the selected supplier
        $products = $this->Products->find()
            ->select(['Products.id', 'Products.name', 'Products.sku'])
            ->distinct(['Products.id'])
            ->innerJoinWith('SupplierProducts') // Association name
            ->where([
                'SupplierProducts.status' => 'A',
                'SupplierProducts.supplier_id' => $supplierId
            ])
            ->order(['Products.name' => 'ASC'])
            ->toArray();

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $products
        ]));
    }

    public function getSupplierProductVariants()
    {
        $this->request->allowMethod(['post']); // Allow only POST requests

        // Get the authenticated user
        $identity = $this->request->getAttribute('identity');

        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 401,
                'message' => 'Unauthorized access'
            ]));
        }

        // Get product_id from POST request data
        $data = $this->request->getData();
        $productId = $data['product_id'] ?? null;

        if (!$productId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'code' => 400,
                'message' => 'Missing product_id in request body.'
            ]));
        }

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku'])
            ->where(['product_id' => $productId, 'status' => 'A'])
            ->toArray();

        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $validVariants = [];

        foreach ($variants as $variant) {
            // Check if this variant exists in SupplierProducts table
            $exists = $this->SupplierProducts->exists([
                'product_variant_id' => $variant->id,
                'status' => 'A'
            ]);

            if ($exists) {
                $validVariants[] = $variant->id;
                $response['variants'][$variant->id] = [
                    'name' => $variant->variant_name,
                    'sku' => $variant->sku
                ];
            }
        }

        // Fetch product attributes if valid variants exist
        if (!empty($validVariants)) {
            $productAttributes = $this->ProductAttributes->find()
                ->contain(['Attributes', 'AttributeValues'])
                ->where([
                    'ProductAttributes.product_id' => $productId,
                    'ProductAttributes.status' => 'A'
                ])
                ->toArray();

            foreach ($productAttributes as $attr) {
                $response['attributes'][] = [
                    'attribute_id' => $attr->id,
                    'attribute_name' => $attr->attribute->name ?? null,
                    'attribute_value' => $attr->attribute_value->value ?? null
                ];
            }
        }

        // Return the response in JSON format
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'data' => $response
        ]));
    }

    public function getWarehouseStockRequestItems()
    {
        $this->request->allowMethod(['post']);

        $identity = $this->request->getAttribute('identity');
        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'code' => 401,
                'message' => 'Unauthorized access',
            ]));
        }

        $data = $this->request->getData();
        $stockRequestId = $data['stock_request_id'] ?? null;

        if (!$stockRequestId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Missing stock_request_id in request.'
            ]));
        }

        $stockRequestItems = $this->StockRequests->StockRequestItems->find()
            ->select([
                'StockRequestItems.id',
                'StockRequestItems.requested_quantity',
                'StockRequestItems.product_id',
                'StockRequestItems.product_variant_id',
                'StockRequestItems.product_attribute_id',
                'StockRequestItems.stock_request_id',
                'StockRequestItems.supervisor_approved_quantity',
                'Products.name',
                'Products.purchase_price',
                'Products.sku',
                'Products.supplier_id',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.purchase_price',
                'ProductVariants.sku',
                'Suppliers.name',
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('Products.Suppliers')
            ->leftJoinWith('ProductVariants')
            ->where(['StockRequestItems.stock_request_id' => $stockRequestId])
            ->enableHydration(false)
            ->toArray();

        $response = [];

        foreach ($stockRequestItems as $item) {
            $attributes = [];

            if (!empty($item['product_attribute_id'])) {
                $attr = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item['product_attribute_id']])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']],
                    ])
                    ->first();

                if ($attr) {
                    $attributes = [
                        'attribute_name' => $attr->attribute->name ?? '',
                        'attribute_value' => $attr->attribute_value->value ?? '',
                    ];
                }
            }

            $productName = $item['_matchingData']['Products']['name'] ?? '';
            $productVariant = $item['_matchingData']['ProductVariants']['variant_name'] ?? 'N/A';
            $productAttribute = !empty($attributes)
                ? $attributes['attribute_name'] . ':' . $attributes['attribute_value']
                : 'N/A';

            $sku = !empty($item['product_variant_id']) && !empty($item['_matchingData']['ProductVariants']['sku'])
                ? $item['_matchingData']['ProductVariants']['sku']
                : ($item['_matchingData']['Products']['sku'] ?? '');

            $supplierName = $item['_matchingData']['Suppliers']['name'] ?? '';

            $response[] = [
                'id' => $item['id'],
                'product_id' => $item['product_id'],
                'product_variant_id' => $item['product_variant_id'],
                'product_attribute_id' => $item['product_attribute_id'],
                'product_name' => $productName,
                'product_variant' => $productVariant,
                'product_attribute' => $productAttribute,
                'sku' => $sku,
                'requested_quantity' => $item['requested_quantity'],
                'supplier_name' => $supplierName,
            ];
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'success' => true,
                'data' => $response
            ]));
    }

    public function incomingStockToWarehouse()
    {
        $this->request->allowMethod(['post']);

        $this->loadComponent('Stock');

        $requested_user = $this->Authentication->getIdentity();

        if (!$requested_user) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Unauthorized'
            ]));
        }

        $role = $this->Roles->get($requested_user->role_id);
        $data = $this->request->getData();

        $allowedFormats = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_TYPE');
        $maxWebImageSize = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE') * 1024 * 1024;

        // Handle document upload
        if (!empty($data['document']) && $data['document']->getError() === UPLOAD_ERR_OK) {
            $web_image = $data['document'];
            $webImageName = trim($web_image->getClientFilename());
            $webImageSize = $web_image->getSize();
            $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

            if (!in_array($webImageExt, $allowedFormats)) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Invalid file type.'
                ]));
            }

            if ($webImageSize > $maxWebImageSize) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'File size too large.'
                ]));
            }

            $webImageTmpName = $web_image->getStream()->getMetadata('uri');
            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $filePath = Configure::read('Constants.INCOMING_STOCK_DOCUMENT');
            $folderPath = $uploadFolder . $filePath;
            $targetdir = WWW_ROOT . $folderPath;
            $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
            $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);

            if ($uploadResult === 'Success') {
                $data['document'] = $uploadFolder . $webImageFile;
            }
        } else {
            $data['document'] = "";
        }

        // Fetch and validate stock request
        $stockRequest = $this->StockRequests->find()
            ->where(['StockRequests.id' => $data['stock_request_id']])
            ->contain(['StockRequestItems'])
            ->first();

        if (!$stockRequest) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Stock request not found.'
            ]));
        }

        // Build stock_items array
        $stock_items = [];
        foreach ($data['product_id'] as $i => $pid) {
            $stock_items[] = [
                'product_id' => $pid,
                'product_variant_id' => $data['product_variant_id'][$i] ?? null,
                'product_attribute_id' => $data['product_attribute_id'][$i] ?? null,
                'quantity' => $data['quantity'][$i] ?? 0
            ];
        }

        $warehouse_id = $stockRequest->warehouse_id;
        $movement_date = $data['movement_date'];
        $referenceID = $data['stock_request_id'];
        $reference_type = 'stock_request';
        $image = $data['document'];

        // Call Stock component
        $result = false;
        if (in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {
            $result = $this->Stock->addWarehouseInStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);
        } elseif (strtolower($role->name) === 'warehouse assistant') {
            $result = $this->Stock->addWarehouseInStockWithoutUpdatingProductStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);
            if ($result) {
                $this->sendIncomingStockApprovalEmail($stockRequest);
            }
        }

        if (!$result) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Failed to process stock request.'
            ]));
        }

        // Post-save updates
        if (in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {
            $stockRequest->request_status = 'Completed';
            $this->StockRequests->save($stockRequest);

            $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
            $stockMovement = $stockMovementsTable->find()
                ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
                ->first();
            if ($stockMovement) {
                $stockMovement->verify_status = 'Approved';
                $stockMovementsTable->save($stockMovement);
            }
        }

        // Supplier PO handling
        $purchaseOrder = $this->SupplierPurchaseOrders->find()
            ->where(['stock_request_id' => $referenceID])
            ->first();

        if ($purchaseOrder) {
            if (in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {
                $purchaseOrder->delivery_status = 'Delivered';
            }

            if (!empty($data['supplier_bill_no'])) {
                $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
            }

            $this->SupplierPurchaseOrders->save($purchaseOrder);

            // Add missing items
            foreach ($stock_items as $item) {
                $existsConditions = [
                    'supplier_purchase_order_id' => $purchaseOrder->id,
                    'product_id' => $item['product_id']
                ];

                if (!empty($item['product_variant_id'])) {
                    $existsConditions['product_variant_id'] = $item['product_variant_id'];
                }
                if (!empty($item['product_attribute_id'])) {
                    $existsConditions['product_attribute_id'] = $item['product_attribute_id'];
                }

                $exists = $this->SupplierPurchaseOrdersItems->exists($existsConditions);

                if (!$exists) {
                    $newItem = $this->SupplierPurchaseOrdersItems->newEntity([
                        'supplier_purchase_order_id' => $purchaseOrder->id,
                        'product_id' => $item['product_id'],
                        'product_variant_id' => $item['product_variant_id'] ?? null,
                        'product_attribute_id' => $item['product_attribute_id'] ?? null,
                        'quantity' => $item['quantity'],
                        'approved_quantity' => $item['quantity']
                    ]);
                    $this->SupplierPurchaseOrdersItems->save($newItem);
                }
            }
        }

        // ✅ Return JSON success response
        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'message' => 'Stock request processed successfully.'
        ]));
    }

    private function sendIncomingStockApprovalEmail($stockRequest)
    {
        $toEmails = [];

        // Fetch Warehouse Manager
        $warehouse = $this->Warehouses->get($stockRequest->warehouse_id, [
                'contain' => ['Managers']
            ]);

        $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
        $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

        $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockRequest->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockRequest->created ? $stockRequest->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockRequest->id,
            'warehouse_name' => $warehouse->name,
            'greeting' => "Dear Warehouse Manager,",
            'message' => "An incoming stock has been added by the warehouse assistant and requires your approval.",
            'request_date' => $formattedDate,
        ];

        $subject = "Approval Required for Incoming Stock Request (ID: #{$stockRequest->id})";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'incoming_stock_approval', // Email template name
            $emailData
        );
    }

    public function approveWarehouseIncomingStock()
    {
        $this->request->allowMethod(['post']);

        try {
            $user_detail = $this->Authentication->getIdentity();
            if (!$user_detail) {
                return $this->response->withType('application/json')
                    ->withStatus(401)
                    ->withStringBody(json_encode(['status' => false, 'message' => 'Unauthorized user']));
            }

            $role = $this->Roles->find()->where(['id' => $user_detail->role_id])->first();
            if (!in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {
                return $this->response->withType('application/json')
                    ->withStatus(403)
                    ->withStringBody(json_encode(['status' => false, 'message' => 'Unauthorized role']));
            }

            $data = $this->request->getData();
            $id = $data['stock_movement_id'] ?? null;

            if (empty($id)) {
                return $this->response->withType('application/json')
                    ->withStatus(400)
                    ->withStringBody(json_encode(['status' => false, 'message' => 'Missing stock_movement_id']));
            }

            $stock_movement_data = $this->StockMovements->get($id);

            $verified_by = $user_detail->id;
            $verify_time = date('Y-m-d H:i:s');

            $product_id = $data['product_id'];
            $product_variant_id = $data['product_variant_id'];
            $product_attribute_id = $data['product_attribute_id'];
            $requested_quantity = $data['quantity'];
            $accepted_quantity = $data['accepted_quantity'];
            $statuses = $data['status'];

            $allRejected = true;

            $this->StockMovementItems->deleteAll(['stock_movement_id' => $id]);

            $stockMovementItems = [];
            for ($i = 0; $i < sizeof($product_id); $i++) {
                $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

                if ($status !== 'Rejected') {
                    $allRejected = false;
                }

                $stockMovementItems[] = [
                    'stock_movement_id' => $id,
                    'product_id' => $product_id[$i],
                    'product_variant_id' => $product_variant_id[$i] ?? null,
                    'product_attribute_id' => $product_attribute_id[$i] ?? null,
                    'quantity' => ($status === 'Rejected') ? $requested_quantity[$i] : $accepted_quantity[$i],
                    'status' => $status
                ];
            }

            $entities = $this->StockMovementItems->newEntities($stockMovementItems);
            if (!$this->StockMovementItems->saveMany($entities)) {
                throw new \Exception("Failed to save stock movement items.");
            }

            if ($allRejected) {
                $stock_movement_data->verify_status = 'Rejected';
                $stock_movement_data->verify_time = $verify_time;
                $stock_movement_data->verified_by = $verified_by;
                $this->StockMovements->save($stock_movement_data);

                $this->incomingStockRejectedEmail($stock_movement_data);
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => true, 'message' => 'All items were rejected.']));
            }

            // Approved path
            $stock_movement_data->verify_status = 'Approved';
            $stock_movement_data->verify_time = $verify_time;
            $stock_movement_data->verified_by = $verified_by;

            if ($this->StockMovements->save($stock_movement_data)) {
                $referenceID = $stock_movement_data->referenceID;
                $stockRequest = $this->StockRequests->get($referenceID);
                if ($stockRequest) {
                    $stockRequest->request_status = 'Completed';
                    $this->StockRequests->save($stockRequest);
                }

                $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
                $stockMovement = $stockMovementsTable->find()
                    ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
                    ->first();

                if ($stockMovement) {
                    $stockMovement->verify_status = 'Approved';
                    $stockMovementsTable->save($stockMovement);
                }

                $supplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');
                $purchaseOrder = $supplierPurchaseOrders->find()
                    ->where(['stock_request_id' => $referenceID])
                    ->first();

                if ($purchaseOrder) {
                    $purchaseOrder->delivery_status = 'Delivered';

                    if (!empty($data['supplier_bill_no'])) {
                        $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
                    }

                    $supplierPurchaseOrders->save($purchaseOrder);

                    $supplierPurchaseOrderItems = TableRegistry::getTableLocator()->get('SupplierPurchaseOrdersItems');

                    $stock_items = $this->StockMovementItems->find()
                        ->where(['stock_movement_id' => $id])
                        ->toArray();

                    $supplierPurchaseOrderItems->deleteAll(['supplier_purchase_order_id' => $purchaseOrder->id]);

                    foreach ($stock_items as $item) {
                        $newItemData = [
                            'supplier_purchase_order_id' => $purchaseOrder->id,
                            'product_id' => $item['product_id'],
                            'quantity' => $item['quantity'],
                            'approved_quantity' => $item['quantity']
                        ];

                        if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                            $newItemData['product_variant_id'] = $item['product_variant_id'];
                        }

                        if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                            $newItemData['product_attribute_id'] = $item['product_attribute_id'];
                        }

                        $newItem = $supplierPurchaseOrderItems->newEntity($newItemData);
                        $supplierPurchaseOrderItems->save($newItem);

                        $this->updateWarehouseProductStocks(
                            'warehouse',
                            $stock_movement_data->showroom_id ?? null,
                            $stock_movement_data->warehouse_id ?? null,
                            $item->product_id ?? null,
                            $item->product_variant_id ?? null,
                            $item->product_attribute_id ?? null,
                            $item->quantity ?? 0
                        );
                    }
                }

                $this->incomingStockApprovedEmail($stock_movement_data);

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => true, 'message' => 'Incoming stock approved successfully.']));
            }

            throw new \Exception("Failed to save stock movement approval.");

        } catch (\Exception $e) {
            return $this->response->withType('application/json')
                ->withStatus(500)
                ->withStringBody(json_encode(['status' => false, 'message' => $e->getMessage()]));
        }
    }

    protected function updateWarehouseProductStocks($requestTo, $showroom_id, $warehouse_id, $product_id, $product_variant_id, $product_attribute_id, $accepted_quantity)
    {
        // Initialize conditions with product_id
        $conditions = [
            'product_id' => $product_id,
        ];

        // Add product_variant_id only if it's not null
        if ($product_variant_id) {
            $conditions['product_variant_id'] = $product_variant_id;
        }

        if ($product_attribute_id) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        // Add either showroom_id or warehouse_id condition
        if ($requestTo === 'showroom' && $showroom_id) {
            $conditions['showroom_id'] = $showroom_id;
        } elseif ($requestTo === 'warehouse' && $warehouse_id) {
            $conditions['warehouse_id'] = $warehouse_id;
        }

        // Fetch product stock
        $stockExist = $this->ProductStocks->find()
            ->where($conditions)
            ->first();

        if ($stockExist) {
            // Update existing stock
            $productStockID = $stockExist->id;
            $existQuantity = $stockExist->quantity;
            $increaseQuantity = $existQuantity + $accepted_quantity;

            // Update only the quantity
            $updateData = ['quantity' => $increaseQuantity];

            // Fetch the stock record and update
            $updateStock = $this->ProductStocks->get($productStockID);
            $updateStock->quantity = $increaseQuantity;

            // Save the updated stock
            $this->ProductStocks->save($updateStock);
        } else {
            // Prepare data for new stock entry
            $newStockData = [
                'product_id' => $product_id,
                'quantity' => $accepted_quantity
            ];

            if ($product_variant_id) {
                $newStockData['product_variant_id'] = $product_variant_id;
            }

            if ($product_attribute_id) {
                $newStockData['product_attribute_id'] = $product_attribute_id;
            }

            if ($requestTo === 'showroom') {
                $newStockData['showroom_id'] = $showroom_id;
            } elseif ($requestTo === 'warehouse') {
                $newStockData['warehouse_id'] = $warehouse_id;
            }

            // Insert new stock record
            $this->ProductStocks->addProductStock($requestTo, $warehouse_id, $newStockData);
        }
    }

    private function incomingStockApprovedEmail($stockMovement)
    {
        $toEmails = [];

        $warehouse = $this->Warehouses->get($stockMovement->warehouse_id, [
                'contain' => ['Assistants']
            ]);

        $warehouseName = $warehouse && $warehouse->assistant ? $warehouse->assistant->first_name.' '.$warehouse->assistant->last_name : 'Unknown Assistant';
        $warehouseAssistantEmail = $warehouse && $warehouse->assistant ? $warehouse->assistant->email : null;

        $toEmails = array_filter([$warehouseAssistantEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockMovement->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockMovement->modified ? $stockMovement->modified->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockMovement->referenceID,
            'warehouse_name' => $warehouse->name ?? 'N/A',
            'greeting' => "Dear {$warehouseName},",
            'message' => "An incoming stock has been successfully added to the warehouse by the manager.",
            'request_date' => $formattedDate,
            'status' => 'Approved'
        ];

        $subject = "Incoming Stock Added - #{$stockMovement->referenceID}";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'incoming_stock_approved', // Email template name
            $emailData
        );
    }

    private function incomingStockRejectedEmail($stockMovement)
    {
        $toEmails = [];

        $warehouse = $this->Warehouses->get($stockMovement->warehouse_id, [
            'contain' => ['Assistants']
        ]);

        $warehouseName = $warehouse && $warehouse->assistant ? $warehouse->assistant->first_name.' '.$warehouse->assistant->last_name : 'Unknown Assistant';
        $warehouseAssistantEmail = $warehouse && $warehouse->assistant ? $warehouse->assistant->email : null;

        $toEmails = array_filter([$warehouseAssistantEmail]);
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for REJECTED incoming stock request ID: " . $stockMovement->id);
            return;
        }

        $formattedDate = $stockMovement->modified ? $stockMovement->modified->format('d-m-y') : 'N/A';

        $emailData = [
            'stock_request_id' => $stockMovement->referenceID,
            'warehouse_name' => $warehouse->name ?? 'N/A',
            'greeting' => "Dear {$warehouseName},",
            'message' => "The incoming stock request was rejected by the warehouse manager.",
            'request_date' => $formattedDate,
            'status' => 'Rejected'
        ];

        $subject = "Incoming Stock Rejected - #{$stockMovement->referenceID}";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'incoming_stock_approved', // Reuse same template
            $emailData
        );
    }

    public function viewWarehouseIncomingStock()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('stock_movement_id');

        if (!$id) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Missing stock_movement_id'
            ]));
        }

        // Step 1: Fetch Stock Movement and supplier ID
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.image',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'StockRequests.supplier_id',        // Fetch supplier_id
                'Suppliers.name',                   // Fetch supplier name
                'SupplierPurchaseOrders.id',        // Purchase order data
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date',
            ])
            ->contain(['Warehouses'])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                'StockMovements.referenceID = StockRequests.id'
            )
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                'StockRequests.supplier_id = Suppliers.id'
            )
            ->leftJoin(
                ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            )
            ->where(['StockMovements.id' => $id])
            ->first();

        if (!$StockMovement) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Stock Movement not found'
            ]));
        }

        $supplierId = $StockMovement['StockRequests']['supplier_id'] ?? null;

        // Step 2: Fetch Stock Movement Items
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_id',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'StockMovementItems.status',
                'Products.name',
                'Products.purchase_price',
                'Products.sku',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.purchase_price',
                'ProductVariants.sku',
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            $item->supplier_price = null;

            $supplierPriceQuery = $this->SupplierProducts->find()
                ->select(['supplier_price'])
                ->where([
                    'SupplierProducts.supplier_id' => $supplierId,
                    'SupplierProducts.product_id' => $item->product_id,
                ]);

            if ($item->product_variant_id) {
                $supplierPriceQuery->where(['SupplierProducts.product_variant_id' => $item->product_variant_id]);
            }

            $supplierPrice = $supplierPriceQuery->first();
            if ($supplierPrice) {
                $item->supplier_price = $supplierPrice->supplier_price;
            }

            // Fetch attribute details
            $item->attributes = [];

            if ($item->product_attribute_id) {
                $attribute = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                if ($attribute) {
                    $item->attributes = [
                        'attribute_name' => $attribute->attribute->name ?? '',
                        'attribute_value' => $attribute->attribute_value->value ?? ''
                    ];
                }
            }
        }

        // Convert image path to full URL if available
        if (!empty($StockMovement->image)) {
            $StockMovement->image = $this->Media->getCloudFrontURL($StockMovement->image);
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => true,
            'data' => [
                'stock_movement' => $StockMovement,
                'items' => $StockMovementItems
            ]
        ]));
    }

    public function getWarehouseOutgoingStocks()
    {
        $this->request->allowMethod(['get']);
        $user = $this->Authentication->getIdentity();

        if (!$user) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => false, 'message' => 'Unauthorized']));
        }

        $role = $this->Roles->get($user->role_id);
        $warehouseId = null;

        if (in_array(strtolower($role->name), ['warehouse manager', 'warehouse assistant'])) {
            $warehouse = $this->Warehouses->find()
                ->select(['id'])
                ->where([
                    strtolower($role->name) === 'warehouse manager' ? 'manager_id' : 'assistant_id' => $user->id
                ])
                ->first();

            if ($warehouse) {
                $warehouseId = $warehouse->id;
            }
        }

        $conditions = ['StockMovements.movement_type' => 'Outgoing'];
        if ($warehouseId !== null) {
            $conditions['StockMovements.warehouse_id'] = $warehouseId;
        } else {
            $conditions['StockMovements.warehouse_id IS NOT'] = null;
        }

        $stockMovements = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'Showrooms.name',
                'total_items' => $this->StockMovements->StockMovementItems->find()->func()->sum('StockMovementItems.quantity'),
                'total_value' => $this->StockMovements->StockMovementItems->find()->func()->sum(
                    'CASE WHEN SupplierProducts.product_id IS NOT NULL THEN COALESCE(SupplierProducts.supplier_price, 0) * StockMovementItems.quantity ELSE 0 END'
                )
            ])
            ->contain(['Warehouses'])
            ->leftJoin(['StockMovementItems' => 'stock_movement_items'], 'StockMovementItems.stock_movement_id = StockMovements.id')
            ->leftJoin(['StockRequests' => 'stock_requests'], 'StockRequests.id = StockMovements.referenceId')
            ->leftJoin(['SupplierProducts' => 'supplier_products'], 'SupplierProducts.product_id = StockMovementItems.product_id AND SupplierProducts.supplier_id = StockRequests.supplier_id')
            ->leftJoin(['Showrooms' => 'showrooms'], 'Showrooms.id = StockRequests.showroom_id')
            ->where($conditions)
            ->group([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.created',
                'Warehouses.name',
                'Showrooms.name'
            ])
            ->order(['StockMovements.id' => 'DESC'])
            ->toArray();

        return $this->response->withType('application/json')
            ->withStringBody(json_encode(['status' => true, 'data' => $stockMovements]));
    }

    public function getRequestIds()
    {
        $this->request->allowMethod(['post']); // Accept only POST requests

        $data = $this->request->getData(); // Get POSTed JSON or form data
        $warehouseId = $data['warehouse_id'] ?? null;
        $showroomId = $data['showroom_id'] ?? null;

        if (!$warehouseId || !$showroomId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => __('Both warehouse and showroom must be provided.'),
            ]));
        }

        // Fetch request IDs with specific conditions
        $requests = $this->StockRequests->find()
            ->select(['id'])
            ->where([
                'StockRequests.requestor_type' => 'Showroom',
                'StockRequests.request_status' => 'Approved',
                'StockRequests.warehouse_id' => $warehouseId,
                'StockRequests.showroom_id' => $showroomId,
                'StockRequests.status' => 'A',
            ])
            ->all();

        // Extract the request IDs into an array
        $requestIds = array_map(function ($request) {
            return $request->id;
        }, $requests->toArray());

        $existingReferenceIds = [];

        if (!empty($requestIds)) {
            // Get referenceIDs from stock_movements that match these request IDs
            $existingReferences = $this->StockMovements->find()
                ->select(['referenceID'])
                ->where([
                    'StockMovements.referenceID IN' => $requestIds
                ])
                ->all();

            $existingReferenceIds = array_map(function ($movement) {
                return $movement->referenceID;
            }, $existingReferences->toArray());
        }

        // Filter out IDs that already exist in stock_movements.referenceID
        $filteredRequestIds = array_diff($requestIds, $existingReferenceIds);

        // Format the result as an array of objects with "id" keys
        $formattedRequestIds = array_map(function ($id) {
            return ['id' => $id];
        }, $filteredRequestIds);

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'data' => array_values($formattedRequestIds),
        ]));
    }

    public function outgoingStockFromWarehouse()
    {
        $this->request->allowMethod(['post']);
        $this->loadComponent('Stock');

        $requestedUser = $this->Authentication->getIdentity();
        if (!$requestedUser) {
            throw new UnauthorizedException("Unauthorized request.");
        }

        $role = $this->Roles->get($requestedUser->role_id);
        $data = $this->request->getData();

        $stockRequestId = $data['stock_request_id'] ?? null;
        $driverId = $data['driver_id'] ?? null;

        if (!$stockRequestId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Stock request id not found.'
            ]));
        }

        try {
            if ($stockRequestId === 'no_request') {
                // Prepare and save new stock request
                $stockRequestData = [
                    'requested_by' => $requestedUser->id,
                    'requestor_type' => 'Showroom',
                    'warehouse_id' => $data['warehouse_id'],
                    'showroom_id' => $data['showroom_id'],
                    'manager_review_status' => 'Pending',
                    'supervisor_verify_status' => 'Approved',
                    'supervisor_verified_time' => date('Y-m-d H:i:s'),
                    'verified_by' => $requestedUser->id,
                    'request_status' => 'Approved',
                    'request_date' => date('Y-m-d H:i:s'),
                    'product_id' => $data['product_id'],
                    'product_variant_id' => $data['product_variant_id'],
                    'product_attribute_id' => $data['product_attribute_id'],
                    'sku' => $data['sku'],
                    'quantity' => $data['quantity']
                ];

                $stockRequest = $this->StockRequests->newEmptyEntity();
                $stockRequest = $this->StockRequests->patchEntity($stockRequest, $stockRequestData);

                if (!$this->StockRequests->save($stockRequest)) {
                    return $this->response->withType('application/json')->withStringBody(json_encode([
                        'status' => 'error',
                        'message' => 'Unable to save stock request.'
                    ]));
                }

                $this->saveStockRequestItems($stockRequest);
                $stockRequestId = $stockRequest->id;
            } else {
                // Update existing request items
                $this->StockRequests->StockRequestItems->deleteAll(['stock_request_id' => $stockRequestId]);

                $productIds = $data['product_id'];
                $productVariantIds = $data['product_variant_id'];
                $productAttributeIds = $data['product_attribute_id'];
                $quantities = $data['quantity'];

                $items = [];
                foreach ($productIds as $i => $pid) {
                    $items[] = [
                        'stock_request_id' => $stockRequestId,
                        'product_id' => $pid,
                        'product_variant_id' => $productVariantIds[$i] ?? null,
                        'product_attribute_id' => $productAttributeIds[$i] ?? null,
                        'requested_quantity' => $quantities[$i],
                        'fulfilled_quantity' => $quantities[$i],
                        'status' => 'Approved'
                    ];
                }

                $entities = $this->StockRequests->StockRequestItems->newEntities($items);
                $this->StockRequests->StockRequestItems->saveMany($entities);
            }

            // Fetch request with approved items
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain(['StockRequestItems' => function ($q) {
                    return $q->where(['StockRequestItems.status' => 'Approved']);
                }])
                ->first();

            if (!$stockRequest) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Stock request not found.'
                ]));
            }

            // Stock movement params
            $stockItems = [];
            foreach ($stockRequest->stock_request_items as $item) {
                $stockItems[] = [
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_attribute_id' => $item->product_attribute_id,
                    'quantity' => $item->fulfilled_quantity
                ];
            }

            $warehouseId = $stockRequest->warehouse_id;
            $movementDate = date('Y-m-d');
            $referenceType = 'stock_request';

            if (in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {
                $result = $this->Stock->addWarehouseOutStock(
                    $warehouseId, $movementDate, $referenceType, $stockRequestId, $stockItems, null, $driverId
                );
            } else {
                $result = $this->Stock->addWarehouseOutStockWithoutUpdatingProductStock(
                    $warehouseId, $movementDate, $referenceType, $stockRequestId, null, $stockItems, $driverId
                );

                if ($result) {
                    $this->sendOutgoingStockApprovalEmail($stockRequest);
                }
            }

            if ($result) {
                if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                    $this->StockMovements->updateAll(
                        [
                            'verify_status' => 'Approved',
                            'verified_by' => $requestedUser->id,
                            'verify_time' => date('Y-m-d H:i:s')
                        ],
                        ['referenceID' => $stockRequestId, 'reference_type' => $referenceType]
                    );

                    $this->sendWarehouseStockDispatchEmail($stockRequest);
                }

                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Stock dispatch processed successfully',
                    'stock_request_id' => $stockRequestId
                ]));
            }

            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Stock movement creation failed.'
            ]));
        } catch (\Exception $e) {
            return $this->response->withType('application/json')->withStatus(400)->withStringBody(json_encode([
                'status' => 'error',
                'message' => $e->getMessage()
            ]));
        }
    }

    private function sendOutgoingStockApprovalEmail($stockRequest)
    {
        $toEmails = [];

        // Fetch Warehouse Manager
        $warehouse = $this->Warehouses->get($stockRequest->warehouse_id, [
                'contain' => ['Managers']
            ]);

        $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
        $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

        $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockRequest->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockRequest->created ? $stockRequest->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockRequest->id,
            'warehouse_name' => $warehouse->name,
            'greeting' => "Dear {$warehouseName},",
            'message' => "An outgoing stock has been added by the warehouse assistant and requires your approval.",
            'request_date' => $formattedDate,
        ];

        $subject = "Outgoing Stock Approval Required - #{$stockRequest->id}";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'outgoing_stock_approval', // Email template name
            $emailData
        );
    }

    private function sendWarehouseStockDispatchEmail($stock_request)
    {
        $toEmails = [];

        // Fetch FROM Showroom (Dispatching Showroom)
        $fromShowroom = $this->Showrooms->get($stock_request->showroom_id, [
            'contain' => ['ShowroomManager']
        ]);
        
        $requestorEmail = $fromShowroom->manager ? $fromShowroom->manager->email : null;

        if (!empty($stock_request->to_showroomID)) {
            // Showroom to Showroom Dispatch
            $toShowroomId = $stock_request->to_showroomID;
            $showroom = $this->Showrooms->get($toShowroomId, [
                'contain' => ['ShowroomManager']
            ]);

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            $toLocation = "Showroom: {$showroom->name}";
            $greeting = "Dear Manager,";

        } elseif (!empty($stock_request->warehouse_id)) {
            // Showroom to Warehouse Dispatch
            $warehouse = $this->Warehouses->get($stock_request->warehouse_id, [
                'contain' => ['Managers']
            ]);

            $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
            $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

            if ($requestorEmail) {
                $toEmails[] = $requestorEmail; // Send to requesting showroom manager
            }

            if ($warehouseManagerEmail) {
                $toEmails[] = $warehouseManagerEmail; // Send to warehouse manager
            }

            $toLocation = "Warehouse: {$warehouse->name}";
            $greeting = "Dear {$warehouseName},";
        }

        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for dispatched stock request ID: " . $stock_request->id);
            return;
        }
        
        $emailData = [
            'request_id' => $stock_request->id,
            'request_status' => 'Dispatched',
            'from_showroom' => $fromShowroom->name,
            'to_location' => $toLocation,
            'greeting' => $greeting,
            'dispatch_date' => date('d-m-Y')
        ];

        $subject = "Stock Dispatch #{$stock_request->id} - Completed";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'stock_dispatch_notification',
            $emailData
        );
    }

    public function approveWarehouseOutgoingStock()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        try {
            $user_detail = $this->Authentication->getIdentity();

            if (!$user_detail) {
                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'error', 'message' => 'Unauthorized']));
            }

            $role = $this->Roles->find()->where(['id' => $user_detail->role_id])->first();

            if (in_array(strtolower($role->name), ['warehouse manager', 'admin'])) {

                $data = $this->request->getData();
                $id = $data['stock_movement_id'] ?? null;

                if (!$id) {
                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode(['status' => 'error', 'message' => 'Stock movement ID is required']));
                }

                $stock_movement_data = $this->StockMovements->get($id);

                $stock_movement_data->verify_status = 'Approved';
                $stock_movement_data->verify_time = date('Y-m-d H:i:s');
                $stock_movement_data->verified_by = $user_detail->id;

                if ($this->StockMovements->save($stock_movement_data)) {
                    $stockRequestId = $stock_movement_data->referenceID;

                    $stockRequest = $this->StockRequests->find()
                        ->where(['StockRequests.id' => $stockRequestId])
                        ->contain(['StockRequestItems' => function ($q) {
                            return $q->where(['StockRequestItems.status' => 'Approved']);
                        }])
                        ->first();

                    if (!$stockRequest) {
                        return $this->response->withType('application/json')
                            ->withStringBody(json_encode(['status' => 'error', 'message' => 'Stock request not found']));
                    }

                    $warehouse_id = $stockRequest->warehouse_id;

                    foreach ($stockRequest->stock_request_items as $item) {
                        $product_id = $item->product_id;
                        $product_variant_id = !empty($item->product_variant_id) && $item->product_variant_id !== 'null' ? $item->product_variant_id : null;
                        $product_attribute_id = !empty($item->product_attribute_id) && $item->product_attribute_id !== 'null' ? $item->product_attribute_id : null;
                        $quantity = $item->fulfilled_quantity ?? 0;

                        $conditions = [
                            'warehouse_id' => $warehouse_id,
                            'product_id' => $product_id
                        ];

                        if ($product_variant_id) {
                            $conditions['product_variant_id'] = $product_variant_id;
                        }
                        if ($product_attribute_id) {
                            $conditions['product_attribute_id'] = $product_attribute_id;
                        }

                        $stockExist = $this->ProductStocks->find()->where($conditions)->first();

                        if ($stockExist) {
                            $this->ProductStocks->updateProductStock($stockExist->id, [
                                'quantity' => max(0, $stockExist->quantity - $quantity),
                                'reduce_reserved_stock' => $quantity
                            ]);
                        } else {
                            $this->ProductStocks->addProductStock('Warehouse', $warehouse_id, [
                                'product_id' => $product_id,
                                'product_variant_id' => $product_variant_id,
                                'product_attribute_id' => $product_attribute_id,
                                'quantity' => $quantity
                            ]);
                        }
                    }

                    // Optional: Send email notification
                    $this->sendWarehouseStockDispatchEmail($stockRequest);

                    return $this->response->withType('application/json')
                        ->withStringBody(json_encode(['status' => 'success', 'message' => 'Stock movement approved successfully']));
                }

                return $this->response->withType('application/json')
                    ->withStringBody(json_encode(['status' => 'error', 'message' => 'Failed to approve the stock movement']));
            }

            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => 'Permission denied']));
        } catch (\Exception $e) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode(['status' => 'error', 'message' => $e->getMessage()]));
        }
    }

    public function viewWarehouseOutgoingStock()
    {
        $this->request->allowMethod(['post']);
        $id = $this->request->getData('stock_movement_id');

        if (!$id) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Missing stock_movement_id'
            ]));
        }

        // Fetch Stock Movement with driver and showroom info
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'Showrooms.name',
                'DriverUser.first_name',
                'DriverUser.last_name'
            ])
            ->contain(['Warehouses'])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                ['StockRequests.id = StockMovements.referenceId']
            )
            ->leftJoin(
                ['Showrooms' => 'showrooms'],
                ['Showrooms.id = StockRequests.showroom_id']
            )
            ->leftJoin(
                ['Drivers' => 'drivers'],
                ['Drivers.id = StockMovements.driver_id']
            )
            ->leftJoin(
                ['DriverUser' => 'users'],
                ['DriverUser.id = Drivers.user_id']
            )
            ->where(['StockMovements.id' => $id])
            ->first();

        if (!$StockMovement) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => false,
                'message' => 'Stock Movement not found'
            ]));
        }

        // Fetch Stock Movement Items
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price',
                'Products.sku',
                'ProductVariants.id',
                'ProductVariants.variant_name',
                'ProductVariants.purchase_price',
                'ProductVariants.sku',
            ])
            ->leftJoinWith('Products')
            ->leftJoinWith('ProductVariants')
            ->where(['StockMovementItems.stock_movement_id' => $id])
            ->toArray();

        foreach ($StockMovementItems as &$item) {
            $item->attributes = [];

            if ($item->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                if ($attributes) {
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => true,
            'data' => [
                'stock_movement' => $StockMovement,
                'items' => $StockMovementItems
            ]
        ]));
    }

    /** SUPPLIER PAYMENTS (ZAID) **/
    public function getSupplierPayments()
    {
        $this->request->allowMethod(['post']);

        $supplierId = $this->request->getData('supplier_id');
        if (empty($supplierId)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => false,
                    'message' => 'supplier_id is required'
                ]));
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '.';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? ',';

        $supplier = $this->Suppliers->find()->where(['id' => $supplierId])->first();
        if (!$supplier) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => false,
                    'message' => 'Supplier not found'
                ]));
        }

        $supplier_payment_raw = $this->SupplierPayment->find()
            ->where(['SupplierPayment.supplier_id' => $supplierId])
            ->contain(['Suppliers', 'SupplierPurchaseOrders'])
            ->order(['SupplierPayment.created' => 'DESC'])
            ->toArray();

        // Format each payment amount
        $supplier_payment = [];
        foreach ($supplier_payment_raw as $payment) {
            $formattedAmount = number_format((float)$payment->amount, 0, '', $thousandSeparator) . ' ' . $currencySymbol;
            $payment->amount = $formattedAmount;
            $supplier_payment[] = $payment;
        }

        $query = $this->SupplierPayment->find()
            ->select(['total_amount' => $this->SupplierPayment->find()->func()->sum('SupplierPayment.amount')])
            ->where(['SupplierPayment.supplier_id' => $supplierId])
            ->innerJoinWith('SupplierPurchaseOrders', function ($q) {
                return $q->where(['SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Paid']]);
            })
            ->first();

        $totalPaymentsDone = $query ? $query->total_amount : 0.00;
        $totalPaymentsDoneFormatted = number_format((float)$totalPaymentsDone, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

        // Pending Bills
        $pendingBills = $this->SupplierPurchaseOrders->find()
            ->contain([
                'SupplierPurchaseOrdersItems' => function ($q) {
                    return $q->contain(['Products'])->select([
                        'SupplierPurchaseOrdersItems.product_id',
                        'SupplierPurchaseOrdersItems.approved_quantity',
                        'SupplierPurchaseOrdersItems.supplier_purchase_order_id'
                    ]);
                }
            ])
            ->where([
                'SupplierPurchaseOrders.payment_status IN' => ['Partially Paid', 'Pending'],
                'SupplierPurchaseOrders.status' => 'A',
                'SupplierPurchaseOrders.supplier_id' => $supplierId
            ])
            ->all();

        $totalPendingPayments = 0;

        foreach ($pendingBills as $purchaseOrder) {
            $orderTotal = 0;
            foreach ($purchaseOrder->supplier_purchase_orders_items as $product) {
                $conditions = [
                    'supplier_id' => $purchaseOrder->supplier_id,
                    'product_id' => $product->product_id,
                    'status' => 'A'
                ];
                if (!empty($product->product_variant_id)) {
                    $conditions['product_variant_id'] = $product->product_variant_id;
                }
                $supplierPrice = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where($conditions)
                    ->first();
                if ($supplierPrice) {
                    $orderTotal += $supplierPrice->supplier_price * $product->approved_quantity;
                }
            }

            $alreadyPaid = $this->SupplierPayment->find()
                ->where(['supplier_purchase_order_id' => $purchaseOrder->id])
                ->select(['sum' => 'SUM(amount)'])
                ->first()->sum ?? 0;

            $pendingAmount = $orderTotal - $alreadyPaid;
            if ($pendingAmount > 0) {
                $totalPendingPayments += $pendingAmount;
            }
        }

        $totalPendingPaymentsFormatted = number_format((float)$totalPendingPayments, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

        // Calculate overdue payments
        $pendingOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'payment_status IN' => ['Partially Paid', 'Pending'],
                'status' => 'A',
                'supplier_id' => $supplierId
            ])
            ->all();

        $totalPendingDue = 0;
        $today = new FrozenDate();

        foreach ($pendingOrders as $order) {
            $currentDueDate = !empty($order->payment_due_date) ? new FrozenDate($order->payment_due_date) : null;
            $newDueDate = $currentDueDate ? $currentDueDate->addDays($supplier->credit_period) : null;

            if ($newDueDate && $today > $newDueDate) {
                $orderTotal = 0;

                $approvedProducts = $this->SupplierPurchaseOrdersItems->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->all();

                foreach ($approvedProducts as $product) {
                    $conditions = [
                        'supplier_id' => $order->supplier_id,
                        'product_id' => $product->product_id,
                        'status' => 'A'
                    ];
                    if (!empty($product->product_variant_id)) {
                        $conditions['product_variant_id'] = $product->product_variant_id;
                    }

                    $supplierProduct = $this->SupplierProducts->find()
                        ->where($conditions)
                        ->first();

                    if ($supplierProduct) {
                        $orderTotal += $supplierProduct->supplier_price * $product->approved_quantity;
                    }
                }

                $alreadyPaid = $this->SupplierPayment->find()
                    ->where(['supplier_purchase_order_id' => $order->id])
                    ->select(['sum' => 'SUM(amount)'])
                    ->first()->sum ?? 0;

                $pendingDue = $orderTotal - $alreadyPaid;

                if ($pendingDue > 0) {
                    $totalPendingDue += $pendingDue;
                }
            }
        }

        $totalPendingDueFormatted = number_format((float)$totalPendingDue, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

        // Final Response
        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => true,
                'message' => 'Supplier payment summary fetched successfully',
                'data' => [
                    'supplier' => $supplier,
                    'payments' => $supplier_payment,
                    'totalPaymentsDone' => $totalPaymentsDoneFormatted,
                    'totalPendingPayments' => $totalPendingPaymentsFormatted,
                    'totalPendingDue' => $totalPendingDueFormatted
                ]
            ]));
    }

    public function viewSupplierPayment()
    {
        $this->request->allowMethod(['post']);
        $paymentId = $this->request->getData('payment_id');

        if (empty($paymentId)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Payment ID is required.'
                ]));
        }

        $supplier_payment = $this->SupplierPayment->get($paymentId, contain: [
            'Suppliers', 'SupplierPurchaseOrders', 'Showrooms'
        ]);

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = $currencyConfig['currency_symbol'] ?? '';
        $decimalSeparator = $currencyConfig['decimal_separator'] ?? '';
        $thousandSeparator = $currencyConfig['thousand_separator'] ?? '';

        // Format the amount
        $supplier_payment->amount = number_format((float)$supplier_payment->amount, 0, '', $thousandSeparator) . ' ' . $currencySymbol;

        // Convert cheque_copy to cloudfront URL
        if ($supplier_payment->cheque_copy) {
            $supplier_payment->cheque_copy = $this->Media->getCloudFrontURL($supplier_payment->cheque_copy);
        }

        // Convert receipt to cloudfront URL
        if ($supplier_payment->receipt) {
            $supplier_payment->receipt = $this->Media->getCloudFrontURL($supplier_payment->receipt);
        }

        // Format payment_date
        if (!empty($supplier_payment->payment_date)) {
            $supplier_payment->payment_date = $supplier_payment->payment_date->format('Y-m-d');
        }

        $response = [
            'status' => 'success',
            'supplier_payment' => $supplier_payment
        ];

        return $this->response
            ->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function addSupplierPayment()
    {
        $this->request->allowMethod(['post']);

        $data = $this->request->getData();

        $user_detail = $this->Authentication->getIdentity();

        $supplierId = $data['supplier_id'] ?? null;
        $showroomId = $data['showroom_id'] ?? null;
        $totalPayment = isset($data['amount']) ? (float)$data['amount'] : 0;

        if ($totalPayment <= 0) {
            throw new BadRequestException(__('Invalid payment amount.'));
        }

        // Upload cheque copy
        $data['cheque_copy'] = $this->_handleFileUpload($data['cheque_copy'] ?? null, 'Constants.SUPPLIER_PAYMENT_CHEQUE_COPY');

        // Upload receipt
        $data['receipt'] = $this->_handleFileUpload($data['receipt'] ?? null, 'Constants.SUPPLIER_PAYMENT_RECEIPT');

        $pendingOrders = $this->SupplierPurchaseOrders->find()
            ->where([
                'supplier_id' => $supplierId,
                'payment_status IN' => ['Partially Paid', 'Pending'],
                'status' => 'A'
            ])
            ->order([
                "FIELD(payment_status, 'Partially Paid', 'Pending')",
                'payment_due_date' => 'ASC'
            ])
            ->all();

        $remainingAmount = $totalPayment;
        $savedPayments = [];

        foreach ($pendingOrders as $order) {
            $orderItems = $this->SupplierPurchaseOrdersItems->find()
                ->where(['supplier_purchase_order_id' => $order->id])
                ->toArray();

            $actualTotal = 0;

            foreach ($orderItems as $item) {
                $supplierPrice = null;

                if (!empty($item->product_variant_id)) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where([
                            'supplier_id' => $supplierId,
                            'product_variant_id' => $item->product_variant_id,
                            'status' => 'A'
                        ])
                        ->first();

                    if ($supplierProduct) {
                        $supplierPrice = $supplierProduct->supplier_price;
                    }
                }

                if ($supplierPrice === null) {
                    $supplierProduct = $this->SupplierProducts->find()
                        ->select(['supplier_price'])
                        ->where([
                            'supplier_id' => $supplierId,
                            'product_id' => $item->product_id,
                            'status' => 'A'
                        ])
                        ->first();

                    if ($supplierProduct) {
                        $supplierPrice = $supplierProduct->supplier_price;
                    }
                }

                if ($supplierPrice !== null) {
                    $actualTotal += $supplierPrice * $item->approved_quantity;
                }
            }

            $alreadyPaid = $this->SupplierPayment->find()
                ->where(['supplier_purchase_order_id' => $order->id])
                ->select(['sum' => 'SUM(amount)'])
                ->first()->sum ?? 0;

            $dueAmount = $actualTotal - $alreadyPaid;

            if ($dueAmount <= 0) {
                continue;
            }

            $paymentAmount = min($dueAmount, $remainingAmount);

            $supplierPayment = $this->SupplierPayment->newEntity([
                'supplier_id' => $supplierId,
                'showroom_id' => $showroomId,
                'supplier_purchase_order_id' => $order->id,
                'payment_mode' => $data['payment_mode'],
                'amount' => $paymentAmount,
                'payment_date' => $data['payment_date'],
                'cheque_no' => $data['cheque_no'] ?? null,
                'cheque_copy' => $data['cheque_copy'],
                'cheque_date' => $data['cheque_date'] ?? null,
                'bank_name' => $data['bank_name'] ?? null,
                'payee_name' => $data['payee_name'] ?? null,
                'receipt_number' => $data['receipt_number'] ?? null,
                'receipt' => $data['receipt'],
                'paid_by' => $user_detail->id ?? null,
            ]);

            if ($this->SupplierPayment->save($supplierPayment)) {
                $savedPayments[] = $supplierPayment;
                $remainingAmount -= $paymentAmount;

                $totalPaid = $alreadyPaid + $paymentAmount;

                if ($totalPaid >= $actualTotal) {
                    $order->payment_status = 'Paid';
                } elseif ($totalPaid > 0) {
                    $order->payment_status = 'Partially Paid';
                }

                $this->SupplierPurchaseOrders->save($order);
            }

            if ($remainingAmount <= 0) {
                break;
            }
        }

        return $this->response->withType('application/json')
            ->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('Payment recorded successfully.'),
                'payments' => $savedPayments
            ]));
    }

    private function _handleFileUpload($file = null, $configPath = '')
    {
        if ($file instanceof \Laminas\Diactoros\UploadedFile && $file->getError() === UPLOAD_ERR_OK) {
            $fileName = trim($file->getClientFilename());
            if (!empty($fileName)) {
                $imageTmpName = $file->getStream()->getMetadata('uri');
                $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                $filePath = Configure::read($configPath);
                $folderPath = $uploadFolder . $filePath;
                $targetdir = WWW_ROOT . $folderPath;
                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;
                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);
                if ($uploadResult === 'Success') {
                    return $folderPath . $imageFile;
                }
            }
        }

        return null;
    }

    /** GLOBAL STOCK SEARCH (ZAID) **/
    public function getProductStockByProduct()
    {
        $this->request->allowMethod(['post']);

        $identity = $this->request->getAttribute('identity');
        if (!$identity) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'code' => 401,
                'message' => 'Unauthorized access',
            ]));
        }

        $data = $this->request->getData();
        $productId = $data['product_id'] ?? null;
        $variantId = $data['product_variant_id'] ?? null;
        $attributeId = $data['product_attribute_id'] ?? null;

        if (!$productId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Missing required product_id.'
            ]));
        }

        // Build conditions
        $conditions = ['product_id' => $productId];

        if (!empty($variantId)) {
            $conditions['product_variant_id'] = $variantId;
        }

        if (!empty($attributeId)) {
            $conditions['product_attribute_id'] = $attributeId;
        }

        $stocks = $this->ProductStocks->find()
            ->where($conditions)
            ->enableHydration(false)
            ->toArray();

        $response = [];

        foreach ($stocks as $stock) {
            if (!empty($stock['showroom_id'])) {
                $showroom = $this->ProductStocks->Showrooms->find()
                    ->select(['name'])
                    ->where(['id' => $stock['showroom_id']])
                    ->first();

                if ($showroom) {
                    $availableQty = (int)$stock['quantity'] - (int)$stock['reserved_stock'];
                    $response[] = [
                        'location' =>  __('Showroom: '.$showroom->name),
                        'quantity' => max(0, $availableQty)
                    ];
                }
            } elseif (!empty($stock['warehouse_id'])) {
                $warehouse = $this->ProductStocks->Warehouses->find()
                    ->select(['name'])
                    ->where(['id' => $stock['warehouse_id']])
                    ->first();

                if ($warehouse) {
                    $availableQty = (int)$stock['quantity'] - (int)$stock['reserved_stock'];
                    $response[] = [
                        'location' => __('Warehouse: '.$warehouse->name),
                        'quantity' => max(0, $availableQty)
                    ];
                }
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => true,
            'data' => $response
        ]));
    }

}   

?>

