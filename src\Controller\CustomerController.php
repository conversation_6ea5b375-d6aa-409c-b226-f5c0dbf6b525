<?php

declare(strict_types=1);

namespace App\Controller;

use App\Controller\AppController;
use App\Controller\Api\V10\ApisController;
use Cake\Controller\Controller;
use Cake\Core\Configure;
use Cake\I18n\DateTime;
use Cake\I18n\FrozenTime;
use Cake\I18n\Time;
use Cake\Utility\Security;
use Cake\Utility\Text;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\Facebook;
use Cake\Routing\Router;
use Authentication\PasswordHasher\DefaultPasswordHasher;

class CustomerController extends Controller
{
    protected $Users;
    protected $Customers;
    protected $ApiRequestLogs;
    protected $OtpVerifications;
    private $useAPI;
    protected $BannerAds;
    protected $Loyalty;
    protected $Carts;
    public function initialize(): void
    {
        parent::initialize();

        $this->Carts = $this->fetchTable('Carts');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Loyalty = $this->fetchTable('Loyalty');

        $this->viewBuilder()->setLayout('web');
        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('Flash');
        $this->loadComponent('WebsiteFunction');
       // $this->loadComponent('Authentication.Authentication');

//        $this->loadComponent('Authentication.Authentication');
        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

    }
    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);
        $topSellCarEmptyItemShow = $this->WebsiteFunction->getTopSellItems(10);
        $scrollContentArr = $this->WebsiteFunction->getHomeScrollText();

        // Pass it to all views
        $this->set(compact('topSellCarEmptyItemShow','scrollContentArr'));
    }
    // Constructor for dependency injection
    public function test()
    {
        $deviceToken = 'eONzpTDNRsamKNZccjXMc5:APA91bEEyUluGjuwyUjVaaGFX5hr00ypjh_AOaVjdBkvv5GbsA-S_8aU1Bc7mzqdNstpGHXwudQpjSUgW8X9eqI9Z9FH1pR_OJzvCljsQ8UfLxBN5T9xOmc';
        if (empty($deviceToken)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => false,
                    'message' => 'Device token is missing.',
                ]));
        }

        $title = 'New Support Request';
        $body  = 'New Support Request has been created. Ticket ID: 123456789';
        $customData = []; // Optional

        $response = $this->Global->sendNotification(
            [$deviceToken],
            $title,
            $body,
            $customData
        );

    return $this->response->withType('application/json')
    ->withStringBody(json_encode($response));


        dd(333);
    }


    public function reset($inputVal = null, $country_code = null, $type = null)
    {

        try {
            if ($this->request->is('get')) {
                // Decode the input values
                $inputVal = $inputVal ? base64_decode($inputVal) : null;
                $type = $type ? base64_decode($type) : null;
                $country_code = $country_code ? base64_decode($country_code) : null;

                // Validate input values
                if (empty($inputVal) || empty($type) || !in_array($type, ['email', 'mobile'])) {
                    $this->Flash->websiteError(__('Invalid or missing input data.'));
                    return $this->redirect(['controller' => 'customer', 'action' => 'signup']);
                }

                $otp_type = $type === 'email' ? 'email' : 'mobile';
                $email = $otp_type === 'email' ? $inputVal : null;
                $mobile = $otp_type === 'mobile' ? $inputVal : null;

                $countryCode = '';
                $mobileNum = '';

                if($mobile){
                    $countryCode = $country_code;
                    $mobileNum = $inputVal;
                    $mobile = $countryCode.$mobileNum;
                    $inputVal = $mobile;
                }

                $this->authenticateUser();
                $this->viewBuilder()->setTemplatePath('customer');

                $otp = $this->generateOTP();
                $time = date('Y-m-d H:i:s');

                $existing_otp = $this->OtpVerifications->find()->where([
                    'user_identifier' => $inputVal,
                    'otp_type' => $otp_type,
                    'is_used' => 0,
                    'expires_at >' => $time
                ])->first();

               // $expiryotptimer = $existing_otp->expires_at ?? '';

                if ($existing_otp) {
                    $otp = $existing_otp['otp'];
                }

                if(empty($existing_otp)) {
                    $otp_saved = $this->OtpVerifications->save_otp_web($inputVal, $otp, $otp_type);
                    if (!$otp_saved) {
                        throw new \Exception(__('OTP could not be saved. Please try again.'));
                    }
                }

                if($mobile) {
                    if(empty($existing_otp)) {
                        $this->Global->sendOtp($mobile, $otp);
                    }
                }
                if ($email) {
                    $user = $this->Users->find()->where(['email' => $email, 'status !=' => 'D'])->first();
                    if (!$user) {
                        throw new \Exception(__('User with this email does not exist.'));
                    }
                    if(empty($existing_otp)) {
                        $to = trim($email);
                        $subject = "Your OTP Code";
                        $template = "customer_otp";
                        $viewVars = ['fullname' => $user->first_name . ' ' . $user->last_name, 'otp' => $otp];
                        $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);

                        if (!$send_email) {
                            throw new \Exception(__('OTP could not be sent. Please try again.'));
                        }
                    }
                }
                $getTimeCheck = $this->OtpVerifications->find()->where([
                    'user_identifier' => $inputVal,
                    'otp_type' => $otp_type,
                    'is_used' => 0,
                    'expires_at >' => $time
                ])->first();

                $expiryotptimer = $getTimeCheck->expires_at ?? '';
           
                $this->set(compact('expiryotptimer','email','countryCode','mobileNum', 'type'));
            }

            if ($this->request->is('post')) {
                $requestData = $this->request->getData();

                if ($requestData['type'] === 'mobile') {

                    if (empty($requestData['country_code']) || empty($requestData['mobile']) || empty($requestData['otp']) || empty($requestData['password'])) {
                        throw new \Exception(__('All fields are required.'));
                    }

                    $data = [
                        "country_code" => $requestData['country_code'],
                        "mobile_no" => $requestData['mobile'],
                        "email" => '',
                        "otp" => $requestData['otp'],
                    ];
                    $user = $this->Users->find()->where(['country_code' => $requestData['country_code'],'mobile_no' => $requestData['mobile'], 'status' => "A"])->first();
                } else {

                    if (empty($requestData['email']) || empty($requestData['otp']) || empty($requestData['password'])) {
                        throw new \Exception(__('All fields are required.'));
                    }

                    $data = [
                        "mobile_no" => '',
                        "email" => $requestData['email'],
                        "otp" => $requestData['otp'],
                    ];
                    $user = $this->Users->find()->where(['email' => $requestData['email'], 'status' => "A"])->first();
                }

                if (!$user) {
                    throw new \Exception(__('No user found with the provided details.'));
                }

                $result = $this->verifyOtp($data);

                if ($result['status'] === 'error') {
                    $this->Flash->websiteError($result['message']);
                    return $this->redirect($this->request->getRequestTarget()); // Redirect to the same page
                }

                $user_attributes = ['password' => $requestData['password'], 'is_email_verified' => 1];
                $this->Users->update_user_by_id($user->id, $user_attributes);

                $this->Flash->websiteSuccess(__('Your password has been updated successfully!'));
                return $this->redirect(['controller' => 'customer', 'action' => 'login']);
            }

        } catch (\Exception $e) {
            $this->Flash->websiteError($e->getMessage());
            return $this->redirect(['controller' => 'customer', 'action' => 'reset']);
        }

        $this->render('reset');
    }


    public function forget()
    {
        $this->viewBuilder()->setTemplatePath('customer');

        if ($this->request->is('post')) {
            $data = $this->request->getData();

            $mobile = $data['mobile_no'];

            // Check if the email is provided and exists
            if (!empty($data['email']) && $data['reset_type'] == "email") {
                $useremail = $this->Users->checkEmailExist(trim($data['email']));
                if (empty($useremail)) {
                    $this->Flash->websiteError(__('Email does not exist.'));
                    return $this->redirect(['controller' => 'customer', 'action' => 'forget', '?' => ['type' => 'email']]);
                } else {
                    return $this->redirect(['controller' => 'customer', 'action' => 'reset', base64_encode(trim($data['email'])), base64_encode(trim('email')), base64_encode(trim('email'))]);
                }
            }

            // Check if the mobile number exists in the database (only if email is not provided)
            if (empty($data['email']) && $data['reset_type'] == "mobile") {
                $usermobile = $this->Users->checkMobileExist(trim($data['mobile_no']), trim($data['phone_code']));
                if (empty($usermobile)) {
                    $this->Flash->websiteError(__('Mobile number does not exist.'));
                    return $this->redirect(['controller' => 'customer', 'action' => 'forget']);
                } else {
                    return $this->redirect(['controller' => 'customer', 'action' => 'reset', base64_encode(trim($data['mobile_no'])),  base64_encode($data['phone_code']), base64_encode(trim('mobile'))]);
                }
            }
        }

        $forgetPageImage = $this->BannerAds->getPageImage('Forget');

        if (!empty($forgetPageImage['web_image'])) {
            $forgetPageImage['web_image'] = $this->Media->getCloudFrontURL($forgetPageImage['web_image']);
        }

        $this->set(compact('forgetPageImage'));
        $this->render('forget');
    }


    public function login()
    {
        $this->convertCart();
        $this->viewBuilder()->setTemplatePath('customer');
        $loginPageImage = $this->BannerAds->getPageImage('Login');
        if (!empty($loginPageImage['web_image'])) {
            $loginPageImage['web_image'] = $this->Media->getCloudFrontURL($loginPageImage['web_image']);
        }
        if ($this->request->is('post')) {
            $data = $this->request->getData();
           
            // browser cache issue fixed
            if($data['login_type'] == 'email'){
                $data['mobile']="";
            }else{
                $data['email']="";
            }
           
            // Check if either mobile_no or email is provided
            if ((isset($data['mobile']) && $data['mobile']) && (isset($data['country_code']) && $data['country_code']) || (isset($data['email']) && $data['email'])) {
                // Find user by mobile_no or email
                $userQuery = $this->Users->find()->where(['status' => 'A']);
                if (isset($data['mobile']) && $data['mobile'] && isset($data['country_code']) && $data['country_code']) {
                    $userQuery = $userQuery->where(['mobile_no' => $data['mobile'], 'country_code' => $data['country_code']]);
                } elseif (isset($data['email']) && $data['email']) {
                    $userQuery = $userQuery->where(['email' => $data['email'], 'user_type' => 'Customer']);
                }
                $user = $userQuery->first();
              
                if (empty($user->password)) {
                    if($data['login_type'] == 'email'){
                        $this->Flash->toast(__("Account not exist"), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                    }else{
                        $this->Flash->toast(__("Account not exist"), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                    }

                    return $this->redirect(['controller' => 'customer', 'action' => 'login']);
                }
                if (isset($data['email']) && !empty($data['email'])) {
                    if($data['email'] && $user->status == "A" && $user->is_email_verified == 0) {
                        $this->Flash->toast(__('Please verify your email address. Your password also works with your mobile number.'), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                        return $this->redirect(['controller' => 'customer', 'action' => 'login']);
                    }
                }

                if ($user && password_verify($data['password'], $user->password)) {
                    if ($user['status'] == "A" && $user['user_type'] == 'Customer') {
                        // Update last login time
                        $user->last_login = FrozenTime::now();
                        $this->Users->save($user);
                        $this->request->getSession()->write('Auth.User', $user);

                        $this->Flash->toast(__('You are logged in successfully!'), [
                            'element' => 'toast',
                            'params' => ['type' => 'success']
                        ]);
                        // Redirect to home page
                        return $this->redirect(['controller' => 'Website', 'action' => 'home']);
                    } else {
                        $this->Flash->toast(__("User is not active or not a customer"), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                    }
                } else {
                    if($data['login_type'] == 'email'){
                        $this->Flash->toast(__("Account not exist"), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                    }else{
                        $this->Flash->toast(__("Account not exist"), [
                            'element' => 'toast',
                            'params' => ['type' => 'warning']
                        ]);
                    }


                }
            } else {
                $this->Flash->toast(__("Invalid parameters"), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
            }
        }
        $this->set(compact('loginPageImage'));
        $this->render('login');
    }

    public function signup()
    {

        $signupPageImage = $this->BannerAds->getPageImage('Signup');
        if (!empty($signupPageImage['web_image'])) {
            $signupPageImage['web_image'] = $this->Media->getCloudFrontURL($signupPageImage['web_image']);
        }
        $this->authenticateUser();
        $this->viewBuilder()->setTemplatePath('customer');
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $mobile = $data['mobile_no'];
            $usermobile = $this->Users->checkMobileExist(trim($mobile), trim($data['phone_code']));
            if ($usermobile) {
                $this->Flash->websiteError(__('Mobile no already exist'));
                return $this->redirect(['controller' => 'customer', 'action' => 'signup']);
            }

            return $this->redirect(['controller' => 'customer', 'action' => 'otp', base64_encode(trim($mobile)), base64_encode(trim($data['phone_code']))]);
        }
        $this->set(compact('signupPageImage'));
        $this->render('signup');

    }

    public function otpCheckRegVerify()
    {

        if ($this->request->is('post')) {
            $this->authenticateUser();
            $this->response = $this->response->withType('application/json');

            $requestData = $this->request->getData();

            $data = array(
                "first_name" => $requestData['first_name'],
                "last_name" => $requestData['last_name'],
                "mobile_no" => $requestData['mobile'],
                "country_code" => $requestData['country_code'],
                "email" => '',
                "otp" => $requestData['otp'],
            );
            $result = $this->verifyOtp($data);
            if ($result['status'] == 'error') {
                $resultData = ['status' => __('error'), 'message' => $result['message']];
                return $this->response->withStringBody(json_encode($resultData));
            } else {
                $data['last_name'] = $requestData['last_name'];
                $data['first_name'] = $requestData['first_name'];
                $data['password'] = $requestData['password'];
                $data['mobile_no'] = $requestData['mobile'];
                $data['user_type'] = 'Customer';
                $data['role_id'] = 3;
                $data['email'] = null;
                $data['status'] = 'A';
                $user_id = $this->Users->add_new_user($data);

                if ($user_id) {
                    $customer_id = $this->Customers->add_new_customer([
                        'user_id' => $user_id,
                        'phone_number' => $requestData['mobile']
                    ]);
                    if ($customer_id) {

                        $userQuery = $this->Users->find()->where(['status' => 'A']);
                        $userQuery = $userQuery->where(['mobile_no' => $requestData['mobile'], 'country_code' => $requestData['country_code']]);
                        $user = $userQuery->first();
                      //  $this->request->getSession()->write('Auth.User', $user);

                        $resultData = ['status' => __('success'), 'message' => __('Your account has been successfully created!')];
                        return $this->response->withStringBody(json_encode($resultData));
                    } else {
                        $resultData = ['status' => __('error'), 'message' => __('Please try again!')];
                    }
                }
                return $this->response->withStringBody(json_encode($resultData));
            }
        }
        return $this->response->withStringBody(json_encode(['status' => __('error'), 'message' => __('someting went wrong!')]));
    }

    public function addCustomerEmail()
    {

        if ($this->request->is('post')) {
            $this->response = $this->response->withType('application/json');

            $data = $this->request->getData();
            if (isset($data['country_code']) && !empty($data['country_code'])) {
                // Remove the '+' sign from the 'country_code'
                $data['country_code'] = trim($data['country_code'], '+');
            }

            $request_attr = [
                'request' => json_encode($data),
                'user_id' => '',
                'api_name' => 'addCustomerEmail',
                'created_at' => Time::now(),
                'updated_at' => Time::now()
            ];


            if (empty($data['mobile_no'])) {
                $resultMsg = ['status' => __('error'), 'message' => __('Mobile number is required')];
                return $this->response->withStringBody(json_encode($resultMsg));
            } else {
                if (empty($data['country_code'])) {
                    $resultMsg = ['status' => __('error'), 'message' => __('Country code is required')];
                    return $this->response->withStringBody(json_encode($resultMsg));
                }
                if (!preg_match('/^[0-9]{10}$/', $data['mobile_no'])) {
                    $resultMsg = ['status' => __('error'), 'message' => __('Enter a valid 10-digit mobile number without a country code')];
                    return $this->response->withStringBody(json_encode($resultMsg));
                }
            }

            if (empty($data['email'])) {
                $resultMsg = ['status' => __('error'), 'message' => __('Email is required')];
                return $this->response->withStringBody(json_encode($resultMsg));
            } else {
                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    $resultMsg = ['status' => __('error'), 'message' => __('Enter valid email address')];
                    return $this->response->withStringBody(json_encode($resultMsg));
                }
            }

            $userid = $this->Users->checkEmailExist(trim($data['email']));

            $user = $this->Users->find()
                ->where(['mobile_no' => $data['mobile_no'], 'country_code' => $data['country_code'], 'status' => 'A'])
                ->first();


            if ($userid) {
                $resultMsg = ['status' => __('error'), 'message' => __('Email already exist')];
                return $this->response->withStringBody(json_encode($resultMsg));
            }

            $token = Security::hash(Security::randomBytes(25));
            $verification_link = Router::url([
                'controller' => 'Users',
                'action' => 'verifyEmail',
                $user['id'] ?? null,
                $token,
                'prefix' => false,
            ], true);

            $to = trim($data['email']);
            $subject = "Email Verification";
            $template = "customer_email_verification";

            $viewVars = [
                'fullname' => ($user->first_name ?? null) . ' ' . ($user->last_name ?? null),
                'verification_link' => $verification_link
            ];

            $send_email = $this->Global->send_email($to, null, $subject, $template, $viewVars);
            if ($send_email) {

                $customer = $this->Customers->find()
                        ->where(['user_id' => $user['id']])
                        ->first();
                if ($customer) {
                    $customer_id = $customer->id;
                    $loyaltyRecord = $this->Loyalty->find()
                        ->where(['customer_id' => $customer_id])
                        ->first();

                    if ($loyaltyRecord) {
                        $loyaltyRecord->points += 20;
                    } else {
                        $loyaltyRecord = $this->Loyalty->newEntity([
                            'customer_id' => $customer_id,
                            'loyalty_category' => 'Standard',
                            'spent_amount' => 0.00,
                            'validity' => date('Y-m-d h:i:s', strtotime('+3 months')),
                            'points' => 20,
                            'status' => 'A',
                        ]);
                    }

                    $this->Loyalty->save($loyaltyRecord);
                }


                $data['email_verify_token'] = $token;
                $data['is_email_verified'] = 0;
                $user_id = $this->Users->editUser($data);

                if ($user_id) {
                    $resultMsg = ['status' => __('success'), 'message' => 'Your email address has been successfully taken! Check your email to verify.'];
                    return $this->response->withStringBody(json_encode($resultMsg));
                } else {
                    $resultMsg = ['status' => __('error'), 'message' => 'Please try again!'];
                    return $this->response->withStringBody(json_encode($resultMsg));
                }
            } else {
                $resultMsg = ['status' => __('error'), 'message' => 'Email sending failed!'];
                return $this->response->withStringBody(json_encode($resultMsg));
            }
        } else {
            $resultMsg = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStringBody(json_encode($resultMsg));
        }
        return $this->response->withStringBody(json_encode($resultMsg));
    }

    public function otp($mobile = null, $country_code = null)
    {
        $this->authenticateUser();
        $this->viewBuilder()->setTemplatePath('customer');
        if (empty($mobile)) {
            $this->Flash->websiteError(__('Mobile Number is required!'));
            return $this->redirect(['controller' => 'customer', 'action' => 'signup']);
        }
        $mobile = base64_decode($mobile);
        $country_code = base64_decode($country_code);
        $otp_type = 'mobile';


        $otp = $this->generateOTP();
        $time = date('Y-m-d H:i:s');
        $existing_otp = $this->OtpVerifications->find()->where(['user_identifier' => $country_code.$mobile, 'otp_type' => 'mobile', 'is_used' => 0, 'expires_at >' => $time])->first();
        if (!empty($existing_otp)) {
            $otp = $existing_otp['otp'];
            $otp_saved = $this->OtpVerifications->save_otp_web($country_code.$mobile, $otp, $otp_type);
        } else {
            $otp_saved = $this->OtpVerifications->save_otp_web($country_code.$mobile, $otp, $otp_type);
            if ($otp_saved) {
                if (!empty($mobile)) {
                    $otpResult = $this->Global->sendOtp($country_code.$mobile, $otp); // enable in production
                }
            }
        }

        if ($this->request->is('post')) {

//            $this->response = $this->response->withType('application/json');
//            $result = ['status' => __('error'), 'message' => __('Otp not valid')];
//            return $this->response->withStringBody(json_encode($result));

            $requestData = $this->request->getData();

            $data = array(
                "mobile_no" => $requestData['mobile'],
                "country_code" => $requestData['country_code'],
                "email" => '',
                "otp" => $requestData['otp'],
            );
            $result = $this->verifyOtp($data);
            if ($result['status'] == 'error') {

                $this->Flash->websiteError($result['message']);

            } else {
                $data['password'] = $requestData['password'];
                $data['mobile_no'] = $requestData['mobile'];
                $data['user_type'] = 'Customer';
                $data['role_id'] = 3;
                $data['email'] = null;
                $data['status'] = 'A';
                $user_id = $this->Users->add_new_user($data);

                if ($user_id) {
                    $customer_id = $this->Customers->add_new_customer([
                        'user_id' => $user_id,
                        'phone_number' => $requestData['mobile']
                    ]);
                    if ($customer_id) {

                        $userQuery = $this->Users->find()->where(['status' => 'A']);
                        $userQuery = $userQuery->where(['mobile_no' => $requestData['mobile'], 'country_code' => $requestData['country_code']]);
                        $user = $userQuery->first();

                        $this->request->getSession()->write('Auth.User', $user);
                        $result = [
                            'status' => __('success'),
                            'code' => 200,
                            'data' => $data,
                            'message' => __('Your account has been successfully created!')
                        ];
                        $this->Flash->websiteSuccess($result['message']);
                        return $this->redirect(['controller' => 'customer', 'action' => 'login']);
                    } else {
                        $result = ['status' => __('error'), 'message' => __('Please try again!')];
                    }
                }
                $this->Flash->websiteError($result['message']);
                return $this->redirect(['controller' => 'customer', 'action' => 'login']);
            }
        }

        $getTimeCheck = $this->OtpVerifications->find()->where([
            'user_identifier' => $country_code.$mobile,
            'otp_type' => $otp_type,
            'is_used' => 0,
            'expires_at >' => $time
        ])->first();

        $expiryotptimer = $getTimeCheck->expires_at ?? '';

        $this->set(compact('expiryotptimer','mobile','country_code'));
        $this->render('otp');
    }

    public function generateOTP()
    {
        $otp = "";
        $charset = "123456789";
        for ($i = 0; $i < 4; $i++) {
            $random_int = mt_rand();
            $otp .= $charset[$random_int % strlen($charset)];
        }
        return $otp;
    }

    // Redirect to social login (Google/Facebook)
    public function socialLogin($provider)
    {
        // Validate the provider directly instead of using 'SocialAuth.'
        if (!in_array($provider, ['google', 'facebook'])) {
            throw new \Cake\Network\Exception\BadRequestException(__('Invalid social provider.'));
        }

        // Handle specific provider configurations
        if ($provider === 'google') {
            $providerClass = new Google([
                'clientId' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_ID'),
                'clientSecret' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_SECRET'),
                'redirectUri' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_REDIRECT_URL'),
            ]);
        } elseif ($provider === 'facebook') {
            $providerClass = new Facebook([
                'clientId' => Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_ID'),
                'clientSecret' => Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_SECRET'),
                'redirectUri' => Configure::read('Settings.SOCIAL_AUTH_FB_REDIRECT_URL'),
            ]);
        } else {
            throw new \Cake\Network\Exception\BadRequestException(__('Unsupported social provider.'));
        }

        // Get the authorization URL and redirect
        $url = $providerClass->getAuthorizationUrl();
        return $this->redirect($url);
    }


    public function socialCallback($provider)
    {
        if (!in_array($provider, ['google', 'facebook'])) {
            throw new \Cake\Network\Exception\BadRequestException(__('Invalid social provider.'));
        }

        // Handle specific provider configurations
        if ($provider === 'google') {
            $providerClass = new Google([
                'clientId' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_ID'),
                'clientSecret' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_CLIENT_SECRET'),
                'redirectUri' => Configure::read('Settings.SOCIAL_AUTH_GOOGLE_REDIRECT_URL'),
            ]);
        } elseif ($provider === 'facebook') {
            // Example configuration for Facebook
            $providerClass = new Facebook([
                'clientId' => Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_ID'),
                'clientSecret' => Configure::read('Settings.SOCIAL_AUTH_FB_CLIENT_SECRET'),
                'redirectUri' => Configure::read('Settings.SOCIAL_AUTH_FB_REDIRECT_URL'),
            ]);
        } else {
            throw new \Cake\Network\Exception\BadRequestException(__('Unsupported social provider.'));
        }

        // Handle the OAuth response
        $token = $providerClass->getAccessToken('authorization_code', [
            'code' => $this->request->getQuery('code'),
        ]);

        $user = $providerClass->getResourceOwner($token);
        $user = $user->toArray();

        // Prepare data for user handling
        $data = [
            'first_name' => $user['given_name'] ?? '',
            'last_name' => $user['family_name'] ?? '',
            'email' => $user['email'] ?? '',
            'uuid' => $user['sub'] ?? '',
        ];
        // Handle the user login or account creation process
        $this->handleSocialUser($data);
    }


    private function handleSocialUser($user)
    {
        $data = $user;

        // Validate email
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $result = ['status' => 'error', 'message' => __('Valid email is required')];
        } else {
            // Check if email already exists
            if ($this->Users->checkEmailExist(trim($data['email']))) {
                $data['id'] = $this->Users->checkEmailExist(trim($data['email']));
                $userQuery = $this->Users->find()->where(['status' => 'A']);
                $userQuery = $userQuery->where(['email' => $data['email']]);
                $user = $userQuery->first();
                $this->request->getSession()->write('Auth.User', $user);
                // $result = ['status' => 'error', 'message' => __('Email already exists')];
            } else {
                // Separate first and last names
                $first_name = $data['first_name'] ?? '';
                $last_name = $data['last_name'] ?? '';

                // Create user and customer entries
                $user_id = $this->Users->add_new_user([
                    "user_type" => 'customer',
                    'first_name' => $first_name,
                    'last_name' => $last_name,
                    'email' => $data['email']
                ]);

                if ($user_id) {
                    $data['id'] = $user_id;
                    $customer_id = $this->Customers->add_new_customer([
                        'user_id' => $user_id
                    ]);

                    if ($customer_id) {
                        $userQuery = $this->Users->find()->where(['status' => 'A']);
                        $userQuery = $userQuery->where(['email' => $data['email']]);
                        $user = $userQuery->first();
                        $this->request->getSession()->write('Auth.User', $user);
                        $result = [
                            'status' => __('success'),
                            'code' => 200,
                            'data' => $data,
                            'message' => __('Your account has been successfully created!')
                        ];
                    } else {
                        $result = ['status' => __('error'), 'message' => __('Please try again!')];
                    }
                } else {
                    $result = ['status' => 'error', 'message' => __('Please try again!')];
                }
            }
        }

        $this->Flash->websiteSuccess(__('You were logged in successfully!'));
        return $this->redirect(['controller' => 'Website', 'action' => 'home']);
    }

    public function verifyOtp($data)
    {
        $request_attr = [
            'request' => json_encode($data),
            'user_id' => '',
            'api_name' => 'verifyOtp'
        ];

        $this->ApiRequestLogs->add_new_log($request_attr);

        // Check if OTP is provided
        if (empty($data['otp'])) {
            $result = [
                'status' => 'error',
                'message' => __('OTP is required')
            ];
            return $this->jsonResponse($result);
        }

        // Check if either mobile number or email is provided
        if (empty($data['mobile_no']) && empty($data['country_code']) && empty($data['email'])) {
            $result = [
                'status' => 'error',
                'message' => __('Mobile number or email is required')
            ];
            return $this->jsonResponse($result);
        }

        // Validate mobile number format
        if (!empty($data['mobile_no']) && !empty($data['country_code'])) {
            if (!preg_match('/^[0-9]{1,4}\s?[0-9]{10}$/', $data['country_code'].$data['mobile_no'])) {
                $result = [
                    'status' => 'error',
                    'message' => __('Enter a valid mobile number with a country code')
                ];
                return $this->jsonResponse($result);
            }
            $user_identifier = $data['country_code'].$data['mobile_no'];
            $otp_type = 'mobile';

        }

        // Validate email format
        if (!empty($data['email'])) {
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $result = [
                    'status' => 'error',
                    'message' => __('Enter valid email address')
                ];
                return $this->jsonResponse($result);
            }
            $user_identifier = $data['email'];
            $otp_type = 'email';
        }

        // Verify OTP
        $otp = $data['otp'];

        $otp_verified = $this->OtpVerifications->verify_otp($user_identifier, $otp, $otp_type);

        // OTP verification responses
        if ($otp_verified == 1) {
            $result = [
                'status' => 'success',
                'code' => 200,
                'data' => [
                    $otp_type => $user_identifier
                ],
                'message' => __('You Account Register Successfully!')
            ];
            return $this->jsonResponse($result);
        } else {
            // Handle different OTP errors (expired, invalid)
            $errorMessages = [
                2 => __('Your OTP has been expired'),
                3 => __('Invalid OTP!')
            ];

            $message = isset($errorMessages[$otp_verified]) ? $errorMessages[$otp_verified] : __('Invalid OTP!');

            $result = [
                'status' => 'error',
                'code' => 200,
                'data' => [
                    $otp_type => $user_identifier
                ],
                'message' => $message
            ];
            return $this->jsonResponse($result);
        }
    }

    // Helper function to return JSON response
    private function jsonResponse($result)
    {
        return $result;
    }

    public function authenticateUser()
    {
        if (!empty($this->request->getSession()->read('Auth.User'))) {
            return $this->redirect(['controller' => 'Website', 'action' => 'home']);
        }
    }

    private function convertCart()
    {
            // Get logged-in user and guest token
            $identity = $this->request->getSession()->read('Auth.User');
            $guestToken = $this->request->getSession()->read('cartId') ?? null; 
            // Check authentication and guest token
            if (!$identity) {
               return false;
            } elseif (empty($guestToken)) {
				return false;
            } else {

                $customerId = $identity->id;
                $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();
                $customerId = $users->customer->id;

                
                $guestCart = $this->Carts->find()
                    ->where(['guest_token' => $guestToken])
                    ->contain(['CartItems'])
                    ->first();
  
                if (!$guestCart) {

						return false;

                } else {

                    $userCart = $this->Carts->find()
                        ->where(['customer_id' => $customerId])
                        ->contain(['CartItems'])
                        ->first();

                    if ($userCart) {
                        foreach ($guestCart->cart_items as $item) {
                            $existingItem = $this->Carts->CartItems->find()
                                ->where(['cart_id' => $userCart->id, 'product_id' => $item->product_id])
                                ->first();

                            if ($existingItem) {
                                $existingItem->quantity += $item->quantity;
                                $this->Carts->CartItems->save($existingItem);
                            } else {
                                $item->cart_id = $userCart->id;
                                $item->isNew(true);
                                $this->Carts->CartItems->save($item);
                            }
                        }
                        return true;
                    } else {
                        $guestCart->customer_id = $customerId;
                        $guestCart->guest_token = null;

                        if ($this->Carts->save($guestCart)) {
                            return true;
                        } else {
                           return false;
                        }
                    }
                }
            }
       
    }


}
