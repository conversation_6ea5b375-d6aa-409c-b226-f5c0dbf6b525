<?php $this->start('add_css'); ?>

<link rel="stylesheet" href="<?= $this->Url->webroot('css/productCategoryListing.css') ?>">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<style>
.productCategoryListing-custom-card-container2 {
    position: initial !important;
}
.custom-card, .custom-card-div, .custom-card-class, .custom-card-recent {
    position: initial !important;
}
.custom-box {
    position : static !important;
}

    .category-header {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .category-item {
        position: relative;
        float: left;
    }

    .category-item label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: all 0.3s;
        justify-content: space-between;
        gap:10px;
        font-size: 18px;
    }

    .category-item label:hover {
        background-color: #f0f0f0;
    }

    .category-item label:before {
        content: '';
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-left: 25px;
        box-sizing: border-box;
        margin-right: 3px;
        font-size: 17px;
        border:1px solid gray;
    }

    .category-item input[type="checkbox"] {
        display: none;
    }

    .category-item input[type="checkbox"]:checked + label:before {
        /* background-color: #f9dab9;
        border-color: #f9dab9; */
        background-color: #ffff;
        border-color: #f9dab9;
        border: 6px solid #EE902C;
    }

    .subcategory {
    margin-top: 5px;
    display: none;
    font-size: 14px;
    list-style: none;
    padding: 0;
    position: relative;
    left: 75px;
    }

    .subcategory-item {
        margin: 5px 0;
        padding: 5px 10px
    }

    .category-item input[type="checkbox"]:checked ~ .subcategory {
        display: block;
    }

    .toggle-icon {
        font-size: 14px;
        color: black;
        transition: transform 0.3s;
    }

    .category-item input[type="checkbox"]:checked + label .toggle-icon {
        transform: rotate(180deg); /* Flip the icon */
    }

    /* .subcategory {
        background: #f9dab9;
    } */
     .custom-card-details .clone-custome-card-name{
        height: 50px;
     }
     .custom-card-details .custom-card-subname{
        margin: 5px 0px 5px 12px;
        height: 21px;
     }
</style>

<style>
    .range_container {
        display: flex;
        flex-direction: column;
        width: 80%;
        margin: 10% auto;
    }

    .sliders_control {
        position: relative;
        min-height: 50px;
    }

    .form_control {
        position: relative;
        display: flex;
        justify-content: space-between;
        font-size: 24px;
        color: #635a5a;
    }

    input[type=range]::-webkit-slider-thumb {
        -webkit-appearance: none;
        pointer-events: all;
        width: 24px;
        height: 24px;
        /* background-color: #fff; */
        background-color: rgb(255, 144, 30);
        border: 3px solid #fff;
        border-radius: 50%;
        box-shadow: 0 0 0 1px rgb(255, 144, 30);
        cursor: pointer;
    }

    input[type=range]::-moz-range-thumb {
        -webkit-appearance: none;
        pointer-events: all;
        width: 24px;
        height: 24px;
        background-color: #fff;
        border-radius: 50%;
        box-shadow: 0 0 0 1px #C6C6C6;
        cursor: pointer;
    }

    input[type=range]::-webkit-slider-thumb:hover {
        background: #f7f7f7;
    }

    input[type=range]::-webkit-slider-thumb:active {
        box-shadow: inset 0 0 3px #387bbe, 0 0 9px #387bbe;
        -webkit-box-shadow: inset 0 0 3px #387bbe, 0 0 9px #387bbe;
    }

    input[type="number"] {
        color: #8a8383;
        width: 100px;
        height: 30px;
        font-size: 13px;
        border: none;
        border-radius: 10px;
        margin-top: -1px;
        outline: 0;
    }
    input[type="text"] {
        color: #8a8383;
        width: 100px;
        height: 30px;
        font-size: 13px;
        border: none;
        border-radius: 10px;
        margin-top: -1px;
        outline: 0;
    }

    input[type=number]::-webkit-inner-spin-button,
    input[type=number]::-webkit-outer-spin-button {
        opacity: 1;
    }

    input[type="range"] {
        -webkit-appearance: none;
        appearance: none;
        height: 2px;
        width: 100%;
        position: absolute;
        background-color: #C6C6C6;
        pointer-events: none;
    }

    #fromSlider {
        height: 0;
        z-index: 1;
    }

    .subcategory .active {
        background: #f9dab9;
        border-radius:7px
    }

    .subcategory-item:hover {
        background: #f9dab9;
        border-radius:7px;
    }
    .custom-card-details{
        height: 75px;
    }
    .custom-card-div{
        height: 418px;
        margin-bottom: 20px !important;
    }
    .custom-box {
        margin-bottom: 20px;
        margin-left: 12px;
    }
    .p-v-p-item-description-add-to-wishlist {
        justify-content: flex-end;
    }
    input.productCategoryListing-search{
        width: 160px;
        font-size: 18px;
    }
    input.search-input {
        margin-top: 8px;
    }
    .product-sort-option{
        height: 54px;
    }
    input.form_control_container__time__input{
        pointer-events: none;
    }
</style>
<?php $this->end(); ?>

<div class="productCategoryListingC">
    <div class="productCategoryListing">
        <img src="<?= $this->Url->webroot('img/icons8-home-100.png') ?>" class="productCategoryListing-home-icn">
        <span
            class="productCategoryListing-home-span"><?= $this->Html->link('Home', ['controller' => 'Website', 'action' => 'home', 'prefix' => false]) ?></span>
        <span class="productCategoryListing-home-span">&gt;</span>


        <?php if(!empty($categoryParentId)): ?>
        <span class="productCategoryListing-home-span Electronic-devices">
        <a href="<?= '/website/product-list/' . $categoryParentId ?>" style="    background: #f9dab9;" class="category-links2">
                <?= $parentCategoryName ?>
                </a>
        </span>
            <span class="productCategoryListing-home-span">&gt;</span>
        <?php endif; ?>

        <span class="productCategoryListing-home-span Electronic-devices"><?= $categoryName ?></span>
    </div>
</div>
<div class="prod-listing-body">

    <div class="productCategoryListing-aside-vertical">

        <div class="productCategoryListing-aside-div">

            <h5 class="productCategoryListing-aside-div-header"><?= __('CATEGORY') ?></h5>
            <form class="productCategoryListing-aside-div-form">
                <?php foreach ($categoryList as $category): ?>
                    <?php
                        // Check if any child category is selected
                        $isMainChecked = false;
                        if (!empty($category->child_categories)) {
                            foreach ($category->child_categories as $child) {
                                if ($categoryId == $child->id) {
                                    $isMainChecked = true;
                                    break;
                                }
                            }
                        }
                    ?>
                    <div class="category-section">
                        <div class="category-item">
                            <input type="checkbox" id="<?= $category->name ?>" <?= $isMainChecked ? 'checked' : '' ?>>
                            <label for="<?= $category->name ?>">
                               <span onclick="redirectToCategory('<?= $category->url_key; ?>')">  <?= $category->name ?> </span>
                                <span class="toggle-icon">▼</span>
                            </label>
                            <?php if ($category->child_categories): ?>
                                <ul class="subcategory">
                                    <?php foreach ($category->child_categories as $val): ?>
                                        <li class="subcategory-item <?= ($categoryId == $val->id) ? 'active' : ''; ?>"
                                            id="productCategoryListing-radio-btn-<?= $val->id; ?>"
                                            <?= ($categoryId == $val->id) ? 'checked' : ''; ?>
                                            onclick="redirectToCategory('<?= $val->url_key; ?>')"
                                        ><?= $val->name ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <ul class="subcategory">
                                    <li class="subcategory-item"><?= __('No Subcategories Found'); ?></li>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </form>


            <hr id="productCategoryListing-hr-radio">

            <h5 class="productCategoryListing-aside-div-header"><?= __('PRICE RANGE') ?></h5>
            <div class="range_container">
                <div class="sliders_control">
                    <input id="fromSlider" name="min_price" type="range"  value="<?= $minPrice ?>" min="0" max="99999"
                           onchange="updateUrl()"/>
                    <input id="toSlider" name="max_price" type="range" value="<?= $maxPrice ?>" min="0" max="99999"
                           onchange="updateUrl()"/>
                </div>
                <div class="form_control">
                    <div class="form_control_container">
                        <div class="form_control_container__time"><?= __('Min') ?></div>
                        <input class="form_control_container__time__input"  onchange="updateUrl()" type="text" id="fromInput"
                               value="<?= $minPrice ?>" min="0" max="99999"/>
                    </div>
                    <div class="form_control_container">
                        <div class="form_control_container__time"><?= __('Max') ?></div>
                        <input class="form_control_container__time__input" onchange="updateUrl()" type="text" id="toInput"
                               value="<?= $maxPrice ?>" min="0" max="99999"/>
                    </div>
                </div>
            </div>

            <hr id="productCategoryListing-hr-radio">

            <h5 class="productCategoryListing-aside-div-header"><?= __('POPULAR BRANDS') ?></h5>


            <div class="productCategoryListing-brands-check-div">
                <?php foreach (array_chunk($BrandsList, 2) as $brandPair): ?> <!-- Group brands into chunks of 2 -->
                    <div class="productCategoryListing-brands-check-double">
                        <?php foreach ($brandPair as $brand): ?>
                            <div class="productCategoryListing-brands-check-label">
                                <input type="checkbox" onchange="updateUrl()" name="popular_brands[]"
                                       class="productCategoryListing-brands-check"
                                    <?php
                                    $selectedBrands = isset($selectedBrands) && is_array($selectedBrands) ? $selectedBrands : []; // Ensure $selectedBrands is an array
                                    echo in_array($brand->id, $selectedBrands) ? 'checked' : '';
                                    ?>
                                       value="<?= h($brand->id); ?>">
                                <label><?= h($brand->name); ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <hr id="productCategoryListing-hr-radio">

            <h5 class="productCategoryListing-aside-div-header"><?= __('RATING') ?></h5>

            <div class="productCategoryListing-rating-stars-div" data-rating="1" onclick="updateUrl(1)">
                <input type="radio" name="ratingFilter" value="1" <?= $rating == 1 ? 'checked' : ''; ?>
                       style="display:none">
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
            </div>
            <div class="productCategoryListing-rating-stars-div" data-rating="2" onclick="updateUrl(2)">
                <input type="radio" name="ratingFilter" value="2" <?= $rating == 2 ? 'checked' : ''; ?>
                       style="display:none"> <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
            </div>
            <!-- Continue similarly for other ratings -->


            <div class="productCategoryListing-rating-stars-div" data-rating="3" onclick="updateUrl(3)">
                <input type="radio" name="ratingFilter" value="3" <?= $rating == 3 ? 'checked' : ''; ?>
                       style="display:none"> <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
            </div>
            <div class="productCategoryListing-rating-stars-div" data-rating="4" onclick="updateUrl(4)">
                <input type="radio" name="ratingFilter" value="4" <?= $rating == 4 ? 'checked' : ''; ?>
                       style="display:none"> <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-two">🟌</span>
            </div>
            <div class="productCategoryListing-rating-stars-div" data-rating="5" onclick="updateUrl(5)">
                <input type="radio" name="ratingFilter" value="5" <?= $rating == 5 ? 'checked' : ''; ?>
                       style="display:none"> <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
                <span class="productCategoryListing-rating-star star-one">🟌</span>
            </div>
        </div>

        <div class="productCategoryListing-add">
            <?php if (!empty($bannerAds['web_image'])): ?>
                <img src="<?= $bannerAds['web_image'] ?>" class="productCategoryListing-add-img">
            <?php endif; ?>
        </div>

    </div>

    <div class="prod-listing-body-splitter">

        <div class="productCategoryListing-search-sort">

            <div>
                <input type="text" placeholder="Search for anything" class="productCategoryListing-search">

            </div>

            <div>
                <div class="product-sort-option">
                    <label style="font-weight:600">Sort By: </label>
                    <select name="sortBy" onchange="updateUrl()" class="productCategoryListing-sort">
                        <option value="popularity" <?= $sortBy == 'popularity' ? 'selected' : ''; ?>> <?= __('Popular') ?>
                        </option>
                        <option value="low-to-high" <?= $sortBy == 'low-to-high' ? 'selected' : ''; ?>><?= __('Price: Low to High') ?>
                        </option>
                        <option value="high-to-low" <?= $sortBy == 'high-to-low' ? 'selected' : ''; ?>><?= __('Price: High to Low') ?>
                        </option>
                        <option value="new-arrival" <?= $sortBy == 'new-arrival' ? 'selected' : ''; ?>><?= __('Newer') ?></option>
                        <option value="discount" <?= $sortBy == 'discount' ? 'selected' : ''; ?>><?= __('Recommended') ?></option>
                    </select>

                </div>
            </div>

        </div>

        <div class="productCategoryListing-active-filters">
    <span class="abacus-span">
        <img src="<?= $this->Url->webroot('img/baby-abacus-toy.png') ?>" class="abacus">
    </span>
            <span class="productCategoryListing-home-span Electronic-devices"><?= __('Active Filters:') ?></span>

            <?php if (!empty($activeFilters)): ?>
                <?php foreach ($activeFilters as $filterKey => $filterValue): ?>
                    <?php
                    // Skip displaying the filter if its value is 'low-to-high'
                    if (
                        $filterValue === 'low-to-high' ||
                        $filterValue === 'high-to-low' ||
                        $filterValue === 'new-arrival' ||
                        $filterValue === 'discount' ||
                        $filterValue === 'popularity' ||
                        (is_array($filterValue) && (
                            in_array('low-to-high', $filterValue) ||
                            in_array('high-to-low', $filterValue) ||
                            in_array('new-arrival', $filterValue) ||
                            in_array('discount', $filterValue) ||
                            in_array('popularity', $filterValue)
                        ))
                    ) {
                        continue;
                    }
                    ?>
                    <span class="productCategoryListing-active-filter-tag">
                        <?= is_array($filterValue) ? implode(', ', $filterValue) : h($filterValue) ?>
                        <span class="productCategoryListing-active-filter-tag-x"
                              onclick="removeFilter('<?= strtolower($filterKey) ?>')">×</span>
                    </span>
                <?php endforeach; ?>
                <?php if (count(array_filter($activeFilters, function($v) { return $v !== 'low-to-high' && !(is_array($v) && in_array('low-to-high', $v)); })) === 0): ?>
                    <span class="productCategoryListing-active-filter-tag"><?= __('No filters applied') ?></span>
                <?php endif; ?>
            <?php else: ?>
                <span class="productCategoryListing-active-filter-tag"><?= __('No filters applied') ?></span>
            <?php endif; ?>
        </div>


        <div class="productCategoryListing-custom-card-container2">

            <?php
            if (!empty($productList)): ?>
                <?php foreach ($productList as $val): ?>
                    <div class="custom-card-div">
                        <?php if ($val->whishlist): ?>
                            <div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                                 data-product-id="<?= $val->id ?>">
                                 <span class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                            </div>
                        <?php else: ?>
                            <div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn for-margin"
                                 data-product-id="<?= $val->id ?>"><span
                                 class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                            </div>
                        <?php endif; ?>
                        <img
                            src="<?= !empty($val->product_image) ? $val->product_image : $this->Url->webroot('img/no-img.jpg'); ?>"
                            class="snd-crsl-img snd-crsl-img-list">
                        <?php
                        $rating = $val->rating;
                        $fullStars = floor($rating);
                        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
                        $emptyStars = 5 - $fullStars - $halfStar;
                        ?>
                        <div class="custom-card-star">
                            <?php for ($i = 0; $i < $fullStars; $i++): ?>
                                ★
                            <?php endfor; ?>
                            <?php if ($halfStar): ?>
                                ☆
                            <?php endif; ?>
                            <?php for ($i = 0; $i < $emptyStars; $i++): ?>
                                ☆
                            <?php endfor; ?>
                            <span class="custom-card-rating"><?= number_format($rating, 1); ?></span>
                        </div>
                        <div class="custom-card-details">
                            <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product', $val->url_key]) ?>">
                                <div class="custom-card-name clone-custom-card-name"><?= h($val->name) ?></div>
                            </a>
                            <div class="custom-card-subname">Reference: <?= $val->product_reference ?></div>
                        </div>
                        <div
                            class="custom-card-price"><?= !empty($val->promotion_price) ? $this->Price->setPriceFormat($val->promotion_price) : 'Price N/A'; ?>
                        <button class="custom-card-add-to-cart-button">
                            <span class="custom-card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                            <span class="custom-card-add-to-cart add-to-cart" data-product-id="<?= $val->id ?>">ADD TO CART</span>
                        </button>
                        </div>
                        <span
                            class="strikethrough-text"><?= !empty($val->sales_price) ? $this->Price->setPriceFormat($val->sales_price) : 'Price N/A'; ?></span>
                        <?php if (!empty($val->discount)): ?>
                            <div class="custom-box">
                                <?= number_format($val->discount, 0); ?>% off
                            </div>
                        <?php endif; ?>

                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p style="text-align: center;"><?= __('No records found.') ?></p>
            <?php endif; ?>
        </div>
        <div id="loading-indicator" style="display: none; margin: 0 auto; width: 100%; text-align: center; padding: 3% 0px;">
            <div class="spinner" style="display: inline-block; width: 40px; height: 40px; border: 4px solid rgba(0, 0, 0, 0.1); border-radius: 50%; border-top-color: #EE902C; animation: spin 1s ease-in-out infinite;"></div>
        </div>

        <div id="no-more-products" style="display: none; margin: 0 auto; width: 100%; text-align: center; padding: 3% 0px; color: #666; font-size: 16px;">
            <?= __('No more products available.') ?>
        </div>

        <style>
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        </style>
    </div>


</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    const baseUrl = "<?= $baseUrl; ?>";

    function redirectToCategory(categoryId) {
        // Construct the URL with the categoryId in the path
        const newUrl = `${baseUrl}/${categoryId}`;

        // Redirect to the new URL
        window.location.href = newUrl;
    }

    function updateUrl(selectedRating) {
        const selectedBrands = [];
        const checkboxes = document.querySelectorAll('input[name="popular_brands[]"]:checked');

        // Collect selected brands
        checkboxes.forEach(checkbox => {
            selectedBrands.push(checkbox.value);
        });

        // Get the min and max price values from input fields
        const minPrice = document.querySelector('input[name="min_price"]').value || '';
        const maxPrice = document.querySelector('input[name="max_price"]').value || '';

        // Get the selected rating value from radio buttons
        const rating = selectedRating || document.querySelector('input[name="ratingFilter"]:checked')?.value || '';

        const url = new URL(window.location.href);

        // Update the URL with the selected brand IDs
        if (selectedBrands.length > 0) {
            url.searchParams.set('brands', selectedBrands.join(','));
        } else {
            url.searchParams.delete('brands');
        }

        // Add min_price and max_price to the URL if provided
        if (minPrice) {
            url.searchParams.set('min_price', minPrice);
        } else {
            url.searchParams.delete('min_price');
        }

        if (maxPrice) {
            url.searchParams.set('max_price', maxPrice);
        } else {
            url.searchParams.delete('max_price');
        }

        const sortBy = document.querySelector('select[name="sortBy"]').value || '';

        // Add the 'sortBy' query parameter to the URL if there's a selected value
        if (sortBy) {
            url.searchParams.set('sortBy', sortBy);
        } else {
            url.searchParams.delete('sortBy');
        }

        // Add rating to the URL if selected
        if (rating) {
            url.searchParams.set('rating', rating);
        } else {
            url.searchParams.delete('rating');
        }

        // Redirect to the updated URL
        window.location.href = url.toString();
    }


    function removeFilter(filterKey) {
        console.log(filterKey);

        const url = new URL(window.location.href);

        if (filterKey === 'sortby') {
            url.searchParams.delete('sortBy');
        }
        if (filterKey === 'price') {
            // Remove both min_price and max_price if the filterKey is 'price'
            url.searchParams.delete('min_price');
            url.searchParams.delete('max_price');
        } else {
            // Remove the filter by key for other filters
            url.searchParams.delete(filterKey);
        }

        // Reload the page with the updated URL
        window.location.href = url.toString();
    }


</script>


<script>
    let delayTimer;

    function controlFromInput(fromSlider, fromInput, toInput, controlSlider) {
        clearTimeout(delayTimer);
        delayTimer = setTimeout(() => {
            const [from, to] = getParsed(fromInput, toInput);
            fillSlider(fromInput, toInput, 'orange', 'orange', controlSlider);
            if (from > to) {
                fromSlider.value = to;
                fromInput.value = to;
            } else {
                fromSlider.value = from;
            }
        }, 300); // Delay of 300ms
    }

    function controlToInput(toSlider, fromInput, toInput, controlSlider) {
        clearTimeout(delayTimer);
        delayTimer = setTimeout(() => {
            const [from, to] = getParsed(fromInput, toInput);
            fillSlider(fromInput, toInput, 'orange', 'orange', controlSlider);
            setToggleAccessible(toInput);
            if (from <= to) {
                toSlider.value = to;
                toInput.value = to;
            } else {
                toInput.value = from;
            }
        }, 1000); // Delay of 300ms
    }

    function controlFromSlider(fromSlider, toSlider, fromInput) {
        const [from, to] = getParsed(fromSlider, toSlider);
        fillSlider(fromSlider, toSlider, 'orange', 'orange', toSlider);
        if (from > to) {
            fromSlider.value = to;
            fromInput.value = to;
        } else {
            fromInput.value = from;
        }
    }

    function controlToSlider(fromSlider, toSlider, toInput) {
        const [from, to] = getParsed(fromSlider, toSlider);
        fillSlider(fromSlider, toSlider, 'orange', 'orange', toSlider);
        setToggleAccessible(toSlider);
        if (from <= to) {
            toSlider.value = to;
            toInput.value = to;
        } else {
            toInput.value = from;
            toSlider.value = from;
        }
    }

    function getParsed(currentFrom, currentTo) {
        const from = parseInt(currentFrom.value, 10);
        const to = parseInt(currentTo.value, 10);
        return [from, to];
    }

    function fillSlider(from, to, sliderColor, rangeColor, controlSlider) {
        const rangeDistance = to.max - to.min;
        const fromPosition = from.value - to.min;
        const toPosition = to.value - to.min;
        controlSlider.style.background = `linear-gradient(
      to right,
      ${sliderColor} 0%,
      ${sliderColor} ${(fromPosition) / (rangeDistance) * 100}%,
      rgb(255, 144, 30) ${((fromPosition) / (rangeDistance)) * 100}%,
      rgb(247, 173, 6) ${(toPosition) / (rangeDistance) * 100}%,
      ${sliderColor} ${(toPosition) / (rangeDistance) * 100}%,
      ${sliderColor} 100%)`;
    }

    function setToggleAccessible(currentTarget) {
        const toSlider = document.querySelector('#toSlider');
        if (Number(currentTarget.value) <= 0) {
            toSlider.style.zIndex = 2;
        } else {
            toSlider.style.zIndex = 0;
        }
    }

    const fromSlider = document.querySelector('#fromSlider');
    const toSlider = document.querySelector('#toSlider');
    const fromInput = document.querySelector('#fromInput');
    const toInput = document.querySelector('#toInput');
    fillSlider(fromSlider, toSlider, '#C6C6C6', '#25daa5', toSlider);
    setToggleAccessible(toSlider);

    fromSlider.oninput = () => controlFromSlider(fromSlider, toSlider, fromInput);
    toSlider.oninput = () => controlToSlider(fromSlider, toSlider, toInput);
    fromInput.oninput = () => controlFromInput(fromSlider, fromInput, toInput, toSlider);
    toInput.oninput = () => controlToInput(toSlider, fromInput, toInput, toSlider);
</script>


<script>
    function getUrlParameter(name) {
        var url = window.location.href;
        var regex = new RegExp('[?&]' + name + '=([^&#]*)', 'i');
        var results = regex.exec(url);
        if (results === null) {
            return null;
        } else {
            return decodeURIComponent(results[1]);
        }
    }

    $(document).ready(function () {
        var page = 2;
        var loading = false;
        var allProductsLoaded = false;

        // Function to load more products
        function loadMoreProducts() {
            if (loading || allProductsLoaded) return;

            loading = true;
            $('#loading-indicator').show();

            // Get the necessary parameters from the URL
            var categoryId = <?= $categoryId ?? 'null' ?>; // Get the current category ID
            var min_price = getUrlParameter('min_price') || 0;
            var max_price = getUrlParameter('max_price') || 99999;
            var sortBy = getUrlParameter('sortBy') || 'popularity';
            var brands = getUrlParameter('brands') || '';
            var rating = getUrlParameter('rating') || '';

            $.ajax({
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                url: "<?= $this->Url->build(['controller' => 'Website', 'action' => 'ajaxLoadMoreProduct']) ?>",
                type: 'post',
                data: {
                    category_id: categoryId,
                    page: page,
                    min_price: min_price,
                    max_price: max_price,
                    sortBy: sortBy,
                    brands: brands,
                    rating: rating,
                    limit: 20 // Set a reasonable limit
                },
                dataType: 'json',
                success: function (response) {
                    // Check if products exist and are not null
                    const products = response.data;

                    if (products && products.length > 0) {
                        products.forEach(val => {
                            var rating = val.rating || 0; // Ensure rating is a number, default to 0
                            var fullStars = Math.floor(rating);
                            var halfStar = (rating - fullStars) >= 0.5 ? 1 : 0;
                            var emptyStars = 5 - fullStars - halfStar;

                            // Generate the HTML for each product
                            var productCard = `
            <div class="custom-card-div">
                ${
                                val.whishlist
                                    ? `<div class="p-v-p-item-description-add-to-wishlist remove-to-wishlist-btn"
                       data-product-id="${val.id}">
                       <span class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-background.png" class="wishlist"> </span>
                   </div>`
                                    : `<div class="p-v-p-item-description-add-to-wishlist add-to-wishlist-btn for-margin"
                       data-product-id="${val.id}">
                       <span class="p-v-p-item-description-add-to-wishlist-heart"> <img src="/assets/heart-nobackground.png" class="wishlist"> </span>
                   </div>`
                            }
                <img src="${val.product_image ? val.product_image : '<?= $this->Url->webroot('img/no-img.jpg') ?>'}" class="snd-crsl-img snd-crsl-img-list">
                <div class="custom-card-star">
                    ${'★'.repeat(fullStars)}
                    ${halfStar ? '☆' : ''}
                    ${'☆'.repeat(emptyStars)}
                    <span class="custom-card-rating">${rating.toFixed(1)}</span>
                </div>
                <div class="custom-card-details">
                    <a href="<?= $this->Url->build(['controller' => 'Website', 'action' => 'product']) ?>/${val.url_key}">
                        <div class="custom-card-name clone-custome-card-name">${val.name}</div>
                    </a>
                    <div class="custom-card-subname">Reference: ${val.product_reference || 'N/A'}</div>
                </div>
                <div class="custom-card-price">${val.promotion_price || 'Price N/A'}
                <button class="custom-card-add-to-cart-button">
                    <span class="custom-card-cart-icon"><i class="fas fa-cart-arrow-down"></i></span>
                    <span class="custom-card-add-to-cart add-to-cart" data-product-id="${val.id}">ADD TO CART</span>
                </button>
                </div>
                <span class="strikethrough-text">${val.sales_price || 'Price N/A'}</span>
                ${val.discount ? `<div class="custom-box">${Number(val.discount).toFixed(0)}% off</div>` : ''}
            </div>
        `;

                            // Append the product card to the correct container
                            $(".productCategoryListing-custom-card-container2").append(productCard);
                        });
                        page++; // Increment page for next load
                    } else {
                        // No more products available
                        allProductsLoaded = true;
                        $('#no-more-products').show();
                    }

                    loading = false;
                    $('#loading-indicator').hide();
                },
                error: function (xhr, status, error) {
                    loading = false;
                    $('#loading-indicator').hide();
                    console.error('Error loading products:', error);

                    // Show error message for a few seconds
                    var errorMsg = $('<div>', {
                        'class': 'error-message',
                        'css': {
                            'position': 'fixed',
                            'bottom': '20px',
                            'right': '20px',
                            'background-color': '#f8d7da',
                            'color': '#721c24',
                            'padding': '10px 15px',
                            'border-radius': '4px',
                            'box-shadow': '0 2px 5px rgba(0,0,0,0.2)',
                            'z-index': '9999'
                        },
                        'text': '<?= __('Error loading products. Please try again.') ?>'
                    }).appendTo('body');

                    // Remove the error message after 5 seconds
                    setTimeout(function() {
                        errorMsg.fadeOut(300, function() { $(this).remove(); });
                    }, 5000);
                }
            });
        }

        // Check if we need to load more products when scrolling
        $(window).scroll(function() {
            // If we're near the bottom of the page
            if($(window).scrollTop() + $(window).height() > $(document).height() - 300) {
                loadMoreProducts();
            }
        });

        // Initial check in case the page is not tall enough to trigger scroll
        if($(window).height() >= $(document).height()) {
            loadMoreProducts();
        }
    })
</script>
